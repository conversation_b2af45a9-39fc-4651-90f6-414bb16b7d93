"""Setup script for the LLM Bounding Box Tool."""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"  Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("✗ Python 3.8 or higher is required")
        print(f"  Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    else:
        print(f"✓ Python version {version.major}.{version.minor}.{version.micro} is compatible")
        return True


def check_pip():
    """Check if pip is available."""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✓ pip is available")
        return True
    except subprocess.CalledProcessError:
        print("✗ pip is not available")
        return False


def install_requirements():
    """Install Python requirements."""
    if not os.path.exists("requirements.txt"):
        print("✗ requirements.txt not found")
        return False
    
    command = f"{sys.executable} -m pip install -r requirements.txt"
    return run_command(command, "Installing Python packages")


def setup_environment():
    """Setup environment configuration."""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy(".env.example", ".env")
            print("✓ Created .env file from .env.example")
            print("  Please edit .env file with your API keys")
        else:
            print("✗ .env.example not found")
            return False
    else:
        print("✓ .env file already exists")
    
    return True


def create_output_directory():
    """Create output directory."""
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    print("✓ Created output directory")
    return True


def run_installation_test():
    """Run the installation test."""
    if os.path.exists("test_installation.py"):
        command = f"{sys.executable} test_installation.py"
        return run_command(command, "Running installation test")
    else:
        print("⚠ test_installation.py not found, skipping test")
        return True


def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "=" * 60)
    print("🎉 Setup completed successfully!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Edit the .env file with your API keys:")
    print("   - For OpenAI: Add your OPENAI_API_KEY")
    print("   - For Anthropic: Add your ANTHROPIC_API_KEY") 
    print("   - For Google: Add your GOOGLE_API_KEY")
    print("\n2. Test the installation:")
    print("   python test_installation.py")
    print("\n3. View available commands:")
    print("   python main.py --help")
    print("\n4. Process your first image:")
    print("   python main.py process-image path/to/your/image.jpg")
    print("\n5. Process a directory of images:")
    print("   python main.py process-directory path/to/your/images/")
    print("\nFor more information, see README.md")


def main():
    """Main setup function."""
    print("LLM Bounding Box Tool - Setup Script")
    print("=" * 40)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # Install requirements
    print("\nInstalling dependencies...")
    if not install_requirements():
        print("Failed to install requirements. Please check the error messages above.")
        sys.exit(1)
    
    # Setup environment
    print("\nSetting up environment...")
    if not setup_environment():
        print("Failed to setup environment.")
        sys.exit(1)
    
    # Create directories
    print("\nCreating directories...")
    if not create_output_directory():
        print("Failed to create output directory.")
        sys.exit(1)
    
    # Run tests
    print("\nTesting installation...")
    if not run_installation_test():
        print("Installation test failed. Please check the error messages above.")
        print("You may still be able to use the tool, but some features might not work.")
    
    # Print next steps
    print_next_steps()


if __name__ == "__main__":
    main()
