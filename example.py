"""Example usage of the LLM bounding box generation tool with folder processing."""

import os
import logging
from config import load_config, setup_logging, validate_api_keys
from llm_client import create_llm_client
from bounding_box_generator import BoundingBoxGenerator
from visualizer import BoundingBoxVisualizer
from image_processor import ImageProcessor


def main():
    """Example of how to use the folder processing functionality."""

    # Load configuration
    config = load_config()
    setup_logging(config)

    try:
        # Validate API keys
        validate_api_keys(config)

        # Initialize components
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        visualizer = BoundingBoxVisualizer(config)
        image_processor = ImageProcessor(config)

        print(f"Using LLM provider: {config.llm_provider}")
        print(f"Model: {getattr(config, f'{config.llm_provider}_model')}")
        print(f"Labels directory: {config.labels_dir}")
        print(f"Visualizations directory: {config.visualizations_dir}")

        # Create output directories
        os.makedirs(config.labels_dir, exist_ok=True)
        os.makedirs(config.visualizations_dir, exist_ok=True)

        # Example folder path - replace with your images folder
        images_folder = "sample_images"  # Replace with your folder path

        if not os.path.exists(images_folder):
            print(f"\nCreating example folder: {images_folder}")
            print("Please add some images to this folder and run the example again.")
            os.makedirs(images_folder, exist_ok=True)
            return

        # Find all images in the folder
        image_files = image_processor.find_images_in_directory(images_folder)

        if not image_files:
            print(f"No images found in {images_folder}")
            print("Please add some images to the folder and try again.")
            return

        print(f"\nFound {len(image_files)} images in {images_folder}")

        # Process first few images as example
        max_examples = min(3, len(image_files))
        processed_images = []
        all_bounding_boxes = []

        for i, image_path in enumerate(image_files[:max_examples]):
            print(f"\nProcessing image {i+1}/{max_examples}: {os.path.basename(image_path)}")

            try:
                # Generate bounding boxes
                bounding_boxes = bbox_generator.generate_bounding_boxes(image_path)

                if bounding_boxes:
                    print(f"  Detected {len(bounding_boxes)} objects:")
                    for bbox in bounding_boxes:
                        print(f"    - {bbox.class_name}: confidence {bbox.confidence:.2f}")

                    # Save YOLO format label
                    base_name = os.path.splitext(os.path.basename(image_path))[0]
                    label_file = os.path.join(config.labels_dir, f"{base_name}.txt")

                    # Get image dimensions
                    width, height = image_processor.get_image_dimensions(image_path)

                    # Write YOLO format
                    with open(label_file, 'w') as f:
                        for bbox in bounding_boxes:
                            class_id = bbox_generator.get_class_id(bbox.class_name)
                            _, x_center, y_center, bbox_width, bbox_height = bbox.to_yolo_format(width, height)
                            f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {bbox_width:.6f} {bbox_height:.6f}\n")

                    print(f"  Saved labels to: {label_file}")

                    # Create visualization
                    viz_path = visualizer.visualize_with_opencv(image_path, bounding_boxes,
                                                              bbox_generator.class_mapping)
                    print(f"  Saved visualization to: {viz_path}")

                    processed_images.append(image_path)
                    all_bounding_boxes.append(bounding_boxes)
                else:
                    print("  No objects detected")
                    all_bounding_boxes.append([])

            except Exception as e:
                print(f"  Error processing {image_path}: {e}")
                continue

        # Save class mapping
        if processed_images:
            classes_file = os.path.join(config.labels_dir, "classes.json")
            with open(classes_file, 'w') as f:
                import json
                json.dump(bbox_generator.class_mapping, f, indent=2)
            print(f"\nSaved class mapping to: {classes_file}")

            # Create summary visualizations
            print("\nCreating summary visualizations...")
            summary_path = visualizer.create_summary_visualization(
                processed_images, all_bounding_boxes, bbox_generator.class_mapping
            )
            print(f"Summary visualization: {summary_path}")

            stats_path = visualizer.create_class_statistics_plot(
                all_bounding_boxes, bbox_generator.class_mapping
            )
            print(f"Class statistics plot: {stats_path}")

        print(f"\nExample completed successfully!")
        print(f"Check the following directories:")
        print(f"  - Labels: {config.labels_dir}")
        print(f"  - Visualizations: {config.visualizations_dir}")

    except Exception as e:
        logging.error(f"Error in example: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
