"""Example usage of the LLM bounding box generation tool."""

import os
import logging
from config import load_config, setup_logging, validate_api_keys
from llm_client import create_llm_client
from bounding_box_generator import BoundingBoxGenerator
from yolo_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON>atter
from image_processor import ImageProcessor


def main():
    """Example of how to use the bounding box generation tool."""
    
    # Load configuration
    config = load_config()
    setup_logging(config)
    
    try:
        # Validate API keys
        validate_api_keys(config)
        
        # Initialize components
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        formatter = YOLOFormatter(config)
        image_processor = ImageProcessor(config)
        
        print(f"Using LLM provider: {config.llm_provider}")
        print(f"Output format: {config.output_format}")
        print(f"Output directory: {config.output_dir}")
        
        # Example 1: Process a single image
        image_path = "example_image.jpg"  # Replace with your image path
        
        if os.path.exists(image_path):
            print(f"\nProcessing single image: {image_path}")
            
            # Generate bounding boxes
            bounding_boxes = bbox_generator.generate_bounding_boxes(image_path)
            
            print(f"Detected {len(bounding_boxes)} objects:")
            for bbox in bounding_boxes:
                print(f"  - {bbox.class_name}: ({bbox.x_min:.1f}, {bbox.y_min:.1f}) to "
                      f"({bbox.x_max:.1f}, {bbox.y_max:.1f}) [confidence: {bbox.confidence:.2f}]")
            
            # Save annotations
            if bounding_boxes:
                output_file = formatter.save_yolo_format(image_path, bounding_boxes, 
                                                        bbox_generator.class_mapping)
                print(f"Annotations saved to: {output_file}")
        
        # Example 2: Process with custom classes
        custom_classes = ["person", "car", "bicycle", "dog"]
        print(f"\nExample with custom classes: {custom_classes}")
        
        if os.path.exists(image_path):
            bounding_boxes = bbox_generator.generate_bounding_boxes(image_path, custom_classes)
            print(f"Detected {len(bounding_boxes)} objects with custom classes")
        
        # Example 3: Add custom class to mapping
        bbox_generator.add_custom_class("custom_object", 100)
        print(f"\nAdded custom class: custom_object -> 100")
        
        # Example 4: Save class mapping
        mapping_file = formatter.save_class_mapping(bbox_generator.class_mapping)
        print(f"Class mapping saved to: {mapping_file}")
        
        # Example 5: Create dataset configuration for YOLO
        if config.output_format == "yolo":
            yaml_file = formatter.create_dataset_yaml(
                bbox_generator.class_mapping,
                train_path="./train/images",
                val_path="./val/images"
            )
            print(f"Dataset configuration saved to: {yaml_file}")
        
        print("\nExample completed successfully!")
        
    except Exception as e:
        logging.error(f"Error in example: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
