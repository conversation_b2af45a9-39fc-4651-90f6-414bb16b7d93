"""Test script to verify the installation and configuration."""

import sys
import os
from typing import List

def test_imports() -> List[str]:
    """Test if all required modules can be imported."""
    errors = []
    
    try:
        import cv2
        print("✓ OpenCV imported successfully")
    except ImportError as e:
        errors.append(f"✗ OpenCV import failed: {e}")
    
    try:
        from PIL import Image
        print("✓ Pillow imported successfully")
    except ImportError as e:
        errors.append(f"✗ Pillow import failed: {e}")
    
    try:
        import numpy as np
        print("✓ NumPy imported successfully")
    except ImportError as e:
        errors.append(f"✗ NumPy import failed: {e}")
    
    try:
        from dotenv import load_dotenv
        print("✓ python-dotenv imported successfully")
    except ImportError as e:
        errors.append(f"✗ python-dotenv import failed: {e}")
    
    try:
        import openai
        print("✓ OpenAI library imported successfully")
    except ImportError as e:
        errors.append(f"✗ OpenAI library import failed: {e}")
    
    try:
        import anthropic
        print("✓ Anthropic library imported successfully")
    except ImportError as e:
        errors.append(f"✗ Anthropic library import failed: {e}")
    
    try:
        import google.generativeai as genai
        print("✓ Google Generative AI imported successfully")
    except ImportError as e:
        errors.append(f"✗ Google Generative AI import failed: {e}")
    
    try:
        import click
        print("✓ Click imported successfully")
    except ImportError as e:
        errors.append(f"✗ Click import failed: {e}")
    
    try:
        import tqdm
        print("✓ tqdm imported successfully")
    except ImportError as e:
        errors.append(f"✗ tqdm import failed: {e}")
    
    try:
        from pydantic import BaseSettings
        print("✓ Pydantic imported successfully")
    except ImportError as e:
        errors.append(f"✗ Pydantic import failed: {e}")
    
    return errors


def test_local_modules() -> List[str]:
    """Test if local modules can be imported."""
    errors = []
    
    try:
        from config import load_config
        print("✓ config module imported successfully")
    except ImportError as e:
        errors.append(f"✗ config module import failed: {e}")
    
    try:
        from llm_client import create_llm_client
        print("✓ llm_client module imported successfully")
    except ImportError as e:
        errors.append(f"✗ llm_client module import failed: {e}")
    
    try:
        from image_processor import ImageProcessor
        print("✓ image_processor module imported successfully")
    except ImportError as e:
        errors.append(f"✗ image_processor module import failed: {e}")
    
    try:
        from bounding_box_generator import BoundingBoxGenerator
        print("✓ bounding_box_generator module imported successfully")
    except ImportError as e:
        errors.append(f"✗ bounding_box_generator module import failed: {e}")
    
    try:
        from yolo_formatter import YOLOFormatter
        print("✓ yolo_formatter module imported successfully")
    except ImportError as e:
        errors.append(f"✗ yolo_formatter module import failed: {e}")
    
    return errors


def test_configuration() -> List[str]:
    """Test configuration loading."""
    errors = []
    
    try:
        from config import load_config
        config = load_config()
        print("✓ Configuration loaded successfully")
        print(f"  - LLM Provider: {config.llm_provider}")
        print(f"  - Output Format: {config.output_format}")
        print(f"  - Max Image Size: {config.max_image_size}")
        print(f"  - Output Directory: {config.output_dir}")
    except Exception as e:
        errors.append(f"✗ Configuration loading failed: {e}")
    
    # Check if .env file exists
    if os.path.exists('.env'):
        print("✓ .env file found")
    else:
        print("⚠ .env file not found (using defaults)")
        print("  Consider copying .env.example to .env and configuring it")
    
    return errors


def test_api_keys() -> List[str]:
    """Test API key configuration."""
    errors = []
    
    try:
        from config import load_config, validate_api_keys
        config = load_config()
        
        # Check which provider is configured
        if config.llm_provider == "openai":
            if config.openai_api_key:
                print("✓ OpenAI API key configured")
            else:
                errors.append("✗ OpenAI API key not configured")
        elif config.llm_provider == "anthropic":
            if config.anthropic_api_key:
                print("✓ Anthropic API key configured")
            else:
                errors.append("✗ Anthropic API key not configured")
        elif config.llm_provider == "google":
            if config.google_api_key:
                print("✓ Google API key configured")
            else:
                errors.append("✗ Google API key not configured")
        
        # Try to validate
        try:
            validate_api_keys(config)
            print("✓ API key validation passed")
        except ValueError as e:
            errors.append(f"✗ API key validation failed: {e}")
            
    except Exception as e:
        errors.append(f"✗ API key check failed: {e}")
    
    return errors


def main():
    """Run all tests."""
    print("LLM Bounding Box Tool - Installation Test")
    print("=" * 50)
    
    all_errors = []
    
    print("\n1. Testing Python package imports...")
    all_errors.extend(test_imports())
    
    print("\n2. Testing local module imports...")
    all_errors.extend(test_local_modules())
    
    print("\n3. Testing configuration...")
    all_errors.extend(test_configuration())
    
    print("\n4. Testing API key configuration...")
    all_errors.extend(test_api_keys())
    
    print("\n" + "=" * 50)
    
    if all_errors:
        print(f"❌ Installation test failed with {len(all_errors)} errors:")
        for error in all_errors:
            print(f"  {error}")
        print("\nPlease fix the errors above before using the tool.")
        sys.exit(1)
    else:
        print("✅ All tests passed! The tool is ready to use.")
        print("\nNext steps:")
        print("1. Configure your API keys in the .env file")
        print("2. Run: python main.py --help")
        print("3. Try: python main.py process-image your_image.jpg")


if __name__ == "__main__":
    main()
