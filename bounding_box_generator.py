"""Core bounding box generation logic using LLM."""

import json
import logging
import re
from typing import List, Dict, Any, Tu<PERSON>, Optional
from dataclasses import dataclass

from config import Config
from llm_client import LLMClient
from image_processor import ImageProcessor

logger = logging.getLogger(__name__)


@dataclass
class BoundingBox:
    """Represents a bounding box with class label."""
    class_name: str
    x_min: float
    y_min: float
    x_max: float
    y_max: float
    confidence: float = 1.0

    def to_yolo_format(self, image_width: int, image_height: int) -> Tuple[str, float, float, float, float]:
        """Convert to YOLO format (class_id, x_center, y_center, width, height) normalized."""
        # Calculate center coordinates and dimensions
        x_center = (self.x_min + self.x_max) / 2.0
        y_center = (self.y_min + self.y_max) / 2.0
        width = self.x_max - self.x_min
        height = self.y_max - self.y_min

        # Normalize to image dimensions
        x_center_norm = x_center / image_width
        y_center_norm = y_center / image_height
        width_norm = width / image_width
        height_norm = height / image_height

        return self.class_name, x_center_norm, y_center_norm, width_norm, height_norm


class BoundingBoxGenerator:
    """Generates bounding boxes for images using LLM."""

    def __init__(self, config: Config, llm_client: LLMClient):
        self.config = config
        self.llm_client = llm_client
        self.image_processor = ImageProcessor(config)

        # Default class mapping (can be customized)
        self.class_mapping = {
            'person': 0,
            'bicycle': 1,
            'car': 2,
            'motorcycle': 3,
            'airplane': 4,
            'bus': 5,
            'train': 6,
            'truck': 7,
            'boat': 8,
            'traffic light': 9,
            'fire hydrant': 10,
            'stop sign': 11,
            'parking meter': 12,
            'bench': 13,
            'bird': 14,
            'cat': 15,
            'dog': 16,
            'horse': 17,
            'sheep': 18,
            'cow': 19,
            'elephant': 20,
            'bear': 21,
            'zebra': 22,
            'giraffe': 23,
            'backpack': 24,
            'umbrella': 25,
            'handbag': 26,
            'tie': 27,
            'suitcase': 28,
            'frisbee': 29,
            'skis': 30,
            'snowboard': 31,
            'sports ball': 32,
            'kite': 33,
            'baseball bat': 34,
            'baseball glove': 35,
            'skateboard': 36,
            'surfboard': 37,
            'tennis racket': 38,
            'bottle': 39,
            'wine glass': 40,
            'cup': 41,
            'fork': 42,
            'knife': 43,
            'spoon': 44,
            'bowl': 45,
            'banana': 46,
            'apple': 47,
            'sandwich': 48,
            'orange': 49,
            'broccoli': 50,
            'carrot': 51,
            'hot dog': 52,
            'pizza': 53,
            'donut': 54,
            'cake': 55,
            'chair': 56,
            'couch': 57,
            'potted plant': 58,
            'bed': 59,
            'dining table': 60,
            'toilet': 61,
            'tv': 62,
            'laptop': 63,
            'mouse': 64,
            'remote': 65,
            'keyboard': 66,
            'cell phone': 67,
            'microwave': 68,
            'oven': 69,
            'toaster': 70,
            'sink': 71,
            'refrigerator': 72,
            'book': 73,
            'clock': 74,
            'vase': 75,
            'scissors': 76,
            'teddy bear': 77,
            'hair drier': 78,
            'toothbrush': 79,
            'milkbox': 80
        }

    def create_detection_prompt(self, custom_classes: Optional[List[str]] = None) -> str:
        """Create a prompt for object detection and bounding box generation."""
        if custom_classes:
            classes_text = ", ".join(custom_classes)
        else:
            classes_text = "any objects you can identify"

        prompt = f"""
        Please analyze this image and identify all objects present. For each object you detect, provide:
        1. The object class/name
        2. Bounding box coordinates in pixel values (x_min, y_min, x_max, y_max)
        3. Confidence level (0.0 to 1.0)

        Focus on detecting: {classes_text}

        Please format your response as a JSON array with the following structure:
        [
            {{
                "class_name": "object_name",
                "x_min": 100,
                "y_min": 150,
                "x_max": 200,
                "y_max": 250,
                "confidence": 0.95
            }},
            ...
        ]

        Important guidelines:
        - Provide coordinates in pixels relative to the original image
        - Be as accurate as possible with bounding box coordinates
        - Only include objects you are confident about (confidence > {self.config.confidence_threshold})
        - Ensure x_min < x_max and y_min < y_max
        - Use descriptive but concise class names
        """

        return prompt

    def parse_llm_response(self, response: str) -> List[BoundingBox]:
        """Parse LLM response and extract bounding boxes."""
        bounding_boxes = []

        try:
            # Try to extract JSON from the response
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)

                for item in data:
                    if all(key in item for key in ['class_name', 'x_min', 'y_min', 'x_max', 'y_max']):
                        bbox = BoundingBox(
                            class_name=item['class_name'].lower().strip(),
                            x_min=float(item['x_min']),
                            y_min=float(item['y_min']),
                            x_max=float(item['x_max']),
                            y_max=float(item['y_max']),
                            confidence=float(item.get('confidence', 1.0))
                        )

                        # Validate bounding box
                        if (bbox.x_min < bbox.x_max and bbox.y_min < bbox.y_max and
                            bbox.confidence >= self.config.confidence_threshold):
                            bounding_boxes.append(bbox)
                        else:
                            logger.warning(f"Invalid bounding box: {bbox}")

            else:
                logger.warning("No JSON found in LLM response")

        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON from LLM response: {e}")
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")

        logger.info(f"Parsed {len(bounding_boxes)} valid bounding boxes")
        return bounding_boxes

    def generate_bounding_boxes(self, image_path: str, custom_classes: Optional[List[str]] = None) -> List[BoundingBox]:
        """Generate bounding boxes for a single image."""
        logger.info(f"Generating bounding boxes for: {image_path}")

        # Preprocess image
        processed_image_path = self.image_processor.preprocess_image(image_path)

        # Create prompt
        prompt = self.create_detection_prompt(custom_classes)

        # Get LLM response
        response = self.llm_client.analyze_image(processed_image_path, prompt)
        logger.debug(f"LLM response: {response}")

        # Parse response
        bounding_boxes = self.parse_llm_response(response)

        # Clean up temporary files if created
        if processed_image_path != image_path:
            import os
            try:
                os.remove(processed_image_path)
            except:
                pass

        return bounding_boxes

    def get_class_id(self, class_name: str) -> int:
        """Get class ID for a given class name."""
        return self.class_mapping.get(class_name.lower(), len(self.class_mapping))

    def add_custom_class(self, class_name: str, class_id: Optional[int] = None) -> None:
        """Add a custom class to the class mapping."""
        if class_id is None:
            class_id = len(self.class_mapping)
        self.class_mapping[class_name.lower()] = class_id
        logger.info(f"Added custom class: {class_name} -> {class_id}")

    def load_class_mapping(self, mapping_file: str) -> None:
        """Load class mapping from a JSON file."""
        try:
            with open(mapping_file, 'r') as f:
                self.class_mapping = json.load(f)
            logger.info(f"Loaded class mapping from {mapping_file}")
        except Exception as e:
            logger.error(f"Error loading class mapping: {e}")
            raise
