"""Main CLI interface for the LLM bounding box generation tool."""

import os
import sys
import logging
from typing import List, Optional
import click
from tqdm import tqdm

from config import load_config, setup_logging, validate_api_keys
from llm_client import create_llm_client
from image_processor import ImageProcessor
from bounding_box_generator import BoundingBoxGenerator
from yolo_formatter import YOLOFormatter

logger = logging.getLogger(__name__)


@click.group()
@click.option('--config-file', default='.env', help='Path to configuration file')
@click.pass_context
def cli(ctx, config_file):
    """LLM-powered bounding box generation tool for YOLO training."""
    # Load configuration
    if config_file != '.env':
        os.environ['ENV_FILE'] = config_file
    
    try:
        config = load_config()
        setup_logging(config)
        validate_api_keys(config)
        
        # Store config in context
        ctx.ensure_object(dict)
        ctx.obj['config'] = config
        
        logger.info("LLM Bounding Box Tool initialized successfully")
        
    except Exception as e:
        click.echo(f"Error initializing tool: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('image_path', type=click.Path(exists=True))
@click.option('--classes', '-c', multiple=True, help='Custom classes to detect')
@click.option('--output-dir', '-o', help='Output directory for annotations')
@click.option('--format', '-f', type=click.Choice(['yolo', 'coco', 'pascal_voc']), 
              default='yolo', help='Output format')
@click.pass_context
def process_image(ctx, image_path, classes, output_dir, format):
    """Process a single image and generate bounding boxes."""
    config = ctx.obj['config']
    
    try:
        # Initialize components
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        formatter = YOLOFormatter(config)
        
        # Set output directory
        if output_dir:
            config.output_dir = output_dir
        
        click.echo(f"Processing image: {image_path}")
        
        # Generate bounding boxes
        custom_classes = list(classes) if classes else None
        bounding_boxes = bbox_generator.generate_bounding_boxes(image_path, custom_classes)
        
        if not bounding_boxes:
            click.echo("No objects detected in the image.")
            return
        
        click.echo(f"Detected {len(bounding_boxes)} objects:")
        for bbox in bounding_boxes:
            click.echo(f"  - {bbox.class_name}: ({bbox.x_min:.1f}, {bbox.y_min:.1f}) to "
                      f"({bbox.x_max:.1f}, {bbox.y_max:.1f}) [confidence: {bbox.confidence:.2f}]")
        
        # Save annotations
        if format == 'yolo':
            output_file = formatter.save_yolo_format(image_path, bounding_boxes, 
                                                    bbox_generator.class_mapping)
        elif format == 'coco':
            output_file = formatter.save_coco_format(image_path, bounding_boxes, 
                                                    bbox_generator.class_mapping)
        elif format == 'pascal_voc':
            output_file = formatter.save_pascal_voc_format(image_path, bounding_boxes)
        
        # Save class mapping
        formatter.save_class_mapping(bbox_generator.class_mapping)
        
        click.echo(f"Annotations saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True))
@click.option('--classes', '-c', multiple=True, help='Custom classes to detect')
@click.option('--output-dir', '-o', help='Output directory for annotations')
@click.option('--format', '-f', type=click.Choice(['yolo', 'coco', 'pascal_voc']), 
              default='yolo', help='Output format')
@click.option('--max-images', type=int, help='Maximum number of images to process')
@click.pass_context
def process_directory(ctx, input_dir, classes, output_dir, format, max_images):
    """Process all images in a directory and generate bounding boxes."""
    config = ctx.obj['config']
    
    try:
        # Initialize components
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        formatter = YOLOFormatter(config)
        image_processor = ImageProcessor(config)
        
        # Set output directory
        if output_dir:
            config.output_dir = output_dir
        
        # Find all images
        image_files = image_processor.find_images_in_directory(input_dir)
        
        if not image_files:
            click.echo("No valid images found in the directory.")
            return
        
        # Limit number of images if specified
        if max_images:
            image_files = image_files[:max_images]
        
        click.echo(f"Processing {len(image_files)} images...")
        
        custom_classes = list(classes) if classes else None
        processed_count = 0
        error_count = 0
        
        # Process images with progress bar
        with tqdm(image_files, desc="Processing images") as pbar:
            for image_path in pbar:
                try:
                    pbar.set_description(f"Processing {os.path.basename(image_path)}")
                    
                    # Generate bounding boxes
                    bounding_boxes = bbox_generator.generate_bounding_boxes(image_path, custom_classes)
                    
                    if bounding_boxes:
                        # Save annotations
                        if format == 'yolo':
                            formatter.save_yolo_format(image_path, bounding_boxes, 
                                                      bbox_generator.class_mapping)
                        elif format == 'coco':
                            formatter.save_coco_format(image_path, bounding_boxes, 
                                                      bbox_generator.class_mapping)
                        elif format == 'pascal_voc':
                            formatter.save_pascal_voc_format(image_path, bounding_boxes)
                        
                        processed_count += 1
                    else:
                        logger.warning(f"No objects detected in {image_path}")
                
                except Exception as e:
                    logger.error(f"Error processing {image_path}: {e}")
                    error_count += 1
                    continue
        
        # Save class mapping and dataset configuration
        formatter.save_class_mapping(bbox_generator.class_mapping)
        
        if format == 'yolo':
            formatter.create_dataset_yaml(bbox_generator.class_mapping)
        
        click.echo(f"\nProcessing complete!")
        click.echo(f"Successfully processed: {processed_count} images")
        click.echo(f"Errors: {error_count} images")
        click.echo(f"Output directory: {config.output_dir}")
        
    except Exception as e:
        logger.error(f"Error processing directory: {e}")
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def list_models(ctx):
    """List available LLM models for each provider."""
    click.echo("Available LLM models:")
    click.echo("\nOpenAI:")
    click.echo("  - gpt-4-vision-preview")
    click.echo("  - gpt-4-turbo")
    
    click.echo("\nAnthropic:")
    click.echo("  - claude-3-sonnet-20240229")
    click.echo("  - claude-3-opus-20240229")
    click.echo("  - claude-3-haiku-20240307")
    
    click.echo("\nGoogle:")
    click.echo("  - gemini-pro-vision")
    click.echo("  - gemini-1.5-pro")


@cli.command()
@click.argument('classes_file', type=click.Path(exists=True))
@click.pass_context
def load_classes(ctx, classes_file):
    """Load custom class mapping from a JSON file."""
    config = ctx.obj['config']
    
    try:
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        bbox_generator.load_class_mapping(classes_file)
        
        click.echo(f"Loaded {len(bbox_generator.class_mapping)} classes from {classes_file}")
        for class_name, class_id in sorted(bbox_generator.class_mapping.items(), key=lambda x: x[1]):
            click.echo(f"  {class_id}: {class_name}")
            
    except Exception as e:
        click.echo(f"Error loading classes: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()
