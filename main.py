"""Main CLI interface for the LLM bounding box generation tool."""

import os
import sys
import logging
import click
from tqdm import tqdm

from config import load_config, setup_logging, validate_api_keys, get_available_models
from llm_client import create_llm_client
from image_processor import ImageProcessor
from bounding_box_generator import BoundingBoxGenerator
from yolo_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from visualizer import BoundingBoxVisualizer

logger = logging.getLogger(__name__)


@click.group()
@click.option('--config-file', default='.env', help='Path to configuration file')
@click.pass_context
def cli(ctx, config_file):
    """LLM-powered bounding box generation tool for YOLO training."""
    # Load configuration
    if config_file != '.env':
        os.environ['ENV_FILE'] = config_file

    try:
        config = load_config()
        setup_logging(config)
        validate_api_keys(config)

        # Store config in context
        ctx.ensure_object(dict)
        ctx.obj['config'] = config

        logger.info("LLM Bounding Box Tool initialized successfully")

    except Exception as e:
        click.echo(f"Error initializing tool: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('folder_path', type=click.Path(exists=True))
@click.option('--classes', '-c', multiple=True, help='Custom classes to detect')
@click.option('--labels-dir', '-l', help='Output directory for label files')
@click.option('--visualizations-dir', '-v', help='Output directory for visualization images')
@click.option('--max-images', type=int, help='Maximum number of images to process')
@click.option('--create-summary', is_flag=True, help='Create summary visualizations')
@click.pass_context
def process_folder(ctx, folder_path, classes, labels_dir, visualizations_dir, max_images, create_summary):
    """Process all images in a folder and generate bounding boxes with visualizations."""
    config = ctx.obj['config']

    try:
        # Initialize components
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        formatter = YOLOFormatter(config)
        visualizer = BoundingBoxVisualizer(config)
        image_processor = ImageProcessor(config)

        # Set output directories
        if labels_dir:
            config.labels_dir = labels_dir
        if visualizations_dir:
            config.visualizations_dir = visualizations_dir

        # Create output directories
        os.makedirs(config.labels_dir, exist_ok=True)
        os.makedirs(config.visualizations_dir, exist_ok=True)

        # Find all images
        image_files = image_processor.find_images_in_directory(folder_path)

        if not image_files:
            click.echo("No valid images found in the folder.")
            return

        # Limit number of images if specified
        if max_images:
            image_files = image_files[:max_images]

        click.echo(f"Processing {len(image_files)} images...")

        # Show standardization info
        if image_processor.should_standardize():
            std_info = image_processor.get_standardization_info()
            click.echo(f"Image standardization enabled: {std_info['target_width']}x{std_info['target_height']} @ {std_info['jpeg_quality']}% quality")

        custom_classes = list(classes) if classes else None
        processed_count = 0
        error_count = 0
        all_bounding_boxes = []
        processed_images = []

        # Process images with progress bar
        with tqdm(image_files, desc="Processing images") as pbar:
            for image_path in pbar:
                try:
                    pbar.set_description(f"Processing {os.path.basename(image_path)}")

                    # Standardize image if enabled
                    processed_image_path = image_path
                    if image_processor.should_standardize():
                        processed_image_path = image_processor.standardize_image(image_path)

                    # Generate bounding boxes
                    bounding_boxes = bbox_generator.generate_bounding_boxes(processed_image_path, custom_classes)

                    if bounding_boxes:
                        # Save YOLO format labels
                        base_name = os.path.splitext(os.path.basename(image_path))[0]
                        label_file = os.path.join(config.labels_dir, f"{base_name}.txt")

                        # Get image dimensions for YOLO format
                        width, height = image_processor.get_image_dimensions(image_path)

                        # Write YOLO format
                        with open(label_file, 'w') as f:
                            for bbox in bounding_boxes:
                                class_id = bbox_generator.get_class_id(bbox.class_name)
                                _, x_center, y_center, bbox_width, bbox_height = bbox.to_yolo_format(width, height)
                                f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {bbox_width:.6f} {bbox_height:.6f}\n")

                        # Create visualization (use processed image)
                        visualizer.visualize_with_opencv(processed_image_path, bounding_boxes,
                                                       bbox_generator.class_mapping)

                        processed_count += 1
                        all_bounding_boxes.append(bounding_boxes)
                        processed_images.append(image_path)
                    else:
                        logger.warning(f"No objects detected in {image_path}")
                        all_bounding_boxes.append([])
                        processed_images.append(image_path)

                except Exception as e:
                    logger.error(f"Error processing {image_path}: {e}")
                    error_count += 1
                    continue

        # Save class mapping
        formatter.save_class_mapping(bbox_generator.class_mapping, config.labels_dir)

        # Create summary visualizations if requested
        if create_summary and processed_images:
            click.echo("Creating summary visualizations...")
            visualizer.create_summary_visualization(processed_images, all_bounding_boxes,
                                                   bbox_generator.class_mapping)
            visualizer.create_class_statistics_plot(all_bounding_boxes, bbox_generator.class_mapping)

        click.echo(f"\nProcessing complete!")
        click.echo(f"Successfully processed: {processed_count} images")
        click.echo(f"Errors: {error_count} images")
        click.echo(f"Labels saved to: {config.labels_dir}")
        click.echo(f"Visualizations saved to: {config.visualizations_dir}")

    except Exception as e:
        logger.error(f"Error processing folder: {e}")
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('image_path', type=click.Path(exists=True))
@click.option('--classes', '-c', multiple=True, help='Custom classes to detect')
@click.option('--labels-dir', '-l', help='Output directory for label files')
@click.option('--visualizations-dir', '-v', help='Output directory for visualization images')
@click.pass_context
def process_image(ctx, image_path, classes, labels_dir, visualizations_dir):
    """Process a single image and generate bounding boxes with visualization."""
    config = ctx.obj['config']

    try:
        # Initialize components
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        visualizer = BoundingBoxVisualizer(config)
        image_processor = ImageProcessor(config)

        # Set output directories
        if labels_dir:
            config.labels_dir = labels_dir
        if visualizations_dir:
            config.visualizations_dir = visualizations_dir

        # Create output directories
        os.makedirs(config.labels_dir, exist_ok=True)
        os.makedirs(config.visualizations_dir, exist_ok=True)

        click.echo(f"Processing image: {image_path}")

        # Standardize image if enabled
        processed_image_path = image_path
        if image_processor.should_standardize():
            click.echo("Standardizing image...")
            processed_image_path = image_processor.standardize_image(image_path)

        # Generate bounding boxes
        custom_classes = list(classes) if classes else None
        bounding_boxes = bbox_generator.generate_bounding_boxes(processed_image_path, custom_classes)

        if not bounding_boxes:
            click.echo("No objects detected in the image.")
            return

        click.echo(f"Detected {len(bounding_boxes)} objects:")
        for bbox in bounding_boxes:
            click.echo(f"  - {bbox.class_name}: ({bbox.x_min:.1f}, {bbox.y_min:.1f}) to "
                      f"({bbox.x_max:.1f}, {bbox.y_max:.1f}) [confidence: {bbox.confidence:.2f}]")

        # Save YOLO format labels
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        label_file = os.path.join(config.labels_dir, f"{base_name}.txt")

        # Get image dimensions for YOLO format (use original image for dimensions)
        width, height = image_processor.get_image_dimensions(image_path)

        # Write YOLO format
        with open(label_file, 'w') as f:
            for bbox in bounding_boxes:
                class_id = bbox_generator.get_class_id(bbox.class_name)
                _, x_center, y_center, bbox_width, bbox_height = bbox.to_yolo_format(width, height)
                f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {bbox_width:.6f} {bbox_height:.6f}\n")

        # Create visualization (use processed image for visualization)
        viz_path = visualizer.visualize_with_opencv(processed_image_path, bounding_boxes, bbox_generator.class_mapping)

        click.echo(f"Labels saved to: {label_file}")
        click.echo(f"Visualization saved to: {viz_path}")

    except Exception as e:
        logger.error(f"Error processing image: {e}")
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('input_path', type=click.Path(exists=True))
@click.option('--output-dir', '-o', help='Output directory for standardized images')
@click.pass_context
def standardize(ctx, input_path, output_dir):
    """Standardize images to consistent size and quality."""
    config = ctx.obj['config']

    try:
        image_processor = ImageProcessor(config)

        if not image_processor.should_standardize():
            click.echo("Image standardization is disabled in configuration.")
            click.echo("Set STANDARDIZE_IMAGES=true in your .env file to enable.")
            return

        std_info = image_processor.get_standardization_info()
        click.echo(f"Standardizing to {std_info['target_width']}x{std_info['target_height']} @ {std_info['jpeg_quality']}% quality")

        if os.path.isfile(input_path):
            # Single image
            click.echo(f"Standardizing image: {input_path}")
            output_path = None
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                filename = os.path.basename(input_path)
                base_name = os.path.splitext(filename)[0]
                output_path = os.path.join(output_dir, f"{base_name}.jpg")

            standardized_path = image_processor.standardize_image(input_path, output_path)
            click.echo(f"Standardized image saved to: {standardized_path}")

        elif os.path.isdir(input_path):
            # Directory of images
            click.echo(f"Standardizing images in directory: {input_path}")
            standardized_files = image_processor.standardize_images_in_directory(input_path, output_dir)
            click.echo(f"Successfully standardized {len(standardized_files)} images")
            if output_dir:
                click.echo(f"Standardized images saved to: {output_dir}")
        else:
            click.echo("Input path must be a file or directory")

    except Exception as e:
        logger.error(f"Error standardizing images: {e}")
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def list_models(ctx):
    """List available OpenRouter models."""
    config = ctx.obj['config']

    click.echo("Configured OpenRouter models:")
    click.echo(f"\nActive model: {config.openrouter_model}")

    available_models = get_available_models(config)
    if available_models:
        click.echo("\nAvailable models from configuration:")
        for name, model_id in available_models.items():
            active = "✓" if model_id == config.openrouter_model else " "
            click.echo(f"  {active} {name}: {model_id}")
    else:
        click.echo("\nNo additional models configured in environment variables.")

    click.echo("\nTo add more models, set these environment variables:")
    click.echo("  MODEL_GEMINI_v2_0_FLASH=google/gemini-2.0-flash-exp:free")
    click.echo("  MODEL_INTERNVL3_14B=opengvlab/internvl3-14b:free")
    click.echo("  MODEL_GEMMA_3_27B_IT=google/gemma-3-27b-it:free")
    click.echo("  MODEL_MISTRALAI_MISTRAL_SMALL_3_1_24B_INSTRUCT=mistralai/mistral-small-3.1-24b-instruct:free")
    click.echo("  MODEL_QWEN_QWEN2_5_VL_72B_INSTRUCT=qwen/qwen2.5-vl-72b-instruct:free")
    click.echo("\nFor complete list, visit: https://openrouter.ai/models")


@cli.command()
@click.argument('classes_file', type=click.Path(exists=True))
@click.pass_context
def load_classes(ctx, classes_file):
    """Load custom class mapping from a JSON file."""
    config = ctx.obj['config']

    try:
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        bbox_generator.load_class_mapping(classes_file)

        click.echo(f"Loaded {len(bbox_generator.class_mapping)} classes from {classes_file}")
        for class_name, class_id in sorted(bbox_generator.class_mapping.items(), key=lambda x: x[1]):
            click.echo(f"  {class_id}: {class_name}")

    except Exception as e:
        click.echo(f"Error loading classes: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()
