"""Main CLI interface for the LLM bounding box generation tool."""

import os
import sys
import logging
from typing import List, Optional
import click
from tqdm import tqdm

from config import load_config, setup_logging, validate_api_keys
from llm_client import create_llm_client
from image_processor import ImageProcessor
from bounding_box_generator import BoundingBoxGenerator
from yolo_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from visualizer import BoundingBoxVisualizer

logger = logging.getLogger(__name__)


@click.group()
@click.option('--config-file', default='.env', help='Path to configuration file')
@click.pass_context
def cli(ctx, config_file):
    """LLM-powered bounding box generation tool for YOLO training."""
    # Load configuration
    if config_file != '.env':
        os.environ['ENV_FILE'] = config_file

    try:
        config = load_config()
        setup_logging(config)
        validate_api_keys(config)

        # Store config in context
        ctx.ensure_object(dict)
        ctx.obj['config'] = config

        logger.info("LLM Bounding Box Tool initialized successfully")

    except Exception as e:
        click.echo(f"Error initializing tool: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('folder_path', type=click.Path(exists=True))
@click.option('--classes', '-c', multiple=True, help='Custom classes to detect')
@click.option('--labels-dir', '-l', help='Output directory for label files')
@click.option('--visualizations-dir', '-v', help='Output directory for visualization images')
@click.option('--max-images', type=int, help='Maximum number of images to process')
@click.option('--create-summary', is_flag=True, help='Create summary visualizations')
@click.pass_context
def process_folder(ctx, folder_path, classes, labels_dir, visualizations_dir, max_images, create_summary):
    """Process all images in a folder and generate bounding boxes with visualizations."""
    config = ctx.obj['config']

    try:
        # Initialize components
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        formatter = YOLOFormatter(config)
        visualizer = BoundingBoxVisualizer(config)
        image_processor = ImageProcessor(config)

        # Set output directories
        if labels_dir:
            config.labels_dir = labels_dir
        if visualizations_dir:
            config.visualizations_dir = visualizations_dir

        # Create output directories
        os.makedirs(config.labels_dir, exist_ok=True)
        os.makedirs(config.visualizations_dir, exist_ok=True)

        # Find all images
        image_files = image_processor.find_images_in_directory(folder_path)

        if not image_files:
            click.echo("No valid images found in the folder.")
            return

        # Limit number of images if specified
        if max_images:
            image_files = image_files[:max_images]

        click.echo(f"Processing {len(image_files)} images...")

        custom_classes = list(classes) if classes else None
        processed_count = 0
        error_count = 0
        all_bounding_boxes = []
        processed_images = []

        # Process images with progress bar
        with tqdm(image_files, desc="Processing images") as pbar:
            for image_path in pbar:
                try:
                    pbar.set_description(f"Processing {os.path.basename(image_path)}")

                    # Generate bounding boxes
                    bounding_boxes = bbox_generator.generate_bounding_boxes(image_path, custom_classes)

                    if bounding_boxes:
                        # Save YOLO format labels
                        base_name = os.path.splitext(os.path.basename(image_path))[0]
                        label_file = os.path.join(config.labels_dir, f"{base_name}.txt")

                        # Get image dimensions for YOLO format
                        width, height = image_processor.get_image_dimensions(image_path)

                        # Write YOLO format
                        with open(label_file, 'w') as f:
                            for bbox in bounding_boxes:
                                class_id = bbox_generator.get_class_id(bbox.class_name)
                                _, x_center, y_center, bbox_width, bbox_height = bbox.to_yolo_format(width, height)
                                f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {bbox_width:.6f} {bbox_height:.6f}\n")

                        # Create visualization
                        visualizer.visualize_with_opencv(image_path, bounding_boxes,
                                                       bbox_generator.class_mapping)

                        processed_count += 1
                        all_bounding_boxes.append(bounding_boxes)
                        processed_images.append(image_path)
                    else:
                        logger.warning(f"No objects detected in {image_path}")
                        all_bounding_boxes.append([])
                        processed_images.append(image_path)

                except Exception as e:
                    logger.error(f"Error processing {image_path}: {e}")
                    error_count += 1
                    continue

        # Save class mapping
        formatter.save_class_mapping(bbox_generator.class_mapping, config.labels_dir)

        # Create summary visualizations if requested
        if create_summary and processed_images:
            click.echo("Creating summary visualizations...")
            visualizer.create_summary_visualization(processed_images, all_bounding_boxes,
                                                   bbox_generator.class_mapping)
            visualizer.create_class_statistics_plot(all_bounding_boxes, bbox_generator.class_mapping)

        click.echo(f"\nProcessing complete!")
        click.echo(f"Successfully processed: {processed_count} images")
        click.echo(f"Errors: {error_count} images")
        click.echo(f"Labels saved to: {config.labels_dir}")
        click.echo(f"Visualizations saved to: {config.visualizations_dir}")

    except Exception as e:
        logger.error(f"Error processing folder: {e}")
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
def list_models():
    """List available LLM models for each provider."""
    click.echo("Available LLM models:")
    click.echo("\nOpenAI:")
    click.echo("  - gpt-4-vision-preview")
    click.echo("  - gpt-4-turbo")

    click.echo("\nAnthropic:")
    click.echo("  - claude-3-sonnet-20240229")
    click.echo("  - claude-3-opus-20240229")
    click.echo("  - claude-3-haiku-20240307")

    click.echo("\nGoogle:")
    click.echo("  - gemini-pro-vision")
    click.echo("  - gemini-1.5-pro")

    click.echo("\nOpenRouter (examples):")
    click.echo("  - anthropic/claude-3.5-sonnet:beta")
    click.echo("  - openai/gpt-4-vision-preview")
    click.echo("  - google/gemini-pro-vision")
    click.echo("  - meta-llama/llama-3.2-90b-vision-instruct")
    click.echo("  - qwen/qwen-2-vl-72b-instruct")
    click.echo("\nFor more OpenRouter models, visit: https://openrouter.ai/models")


@cli.command()
@click.argument('classes_file', type=click.Path(exists=True))
@click.pass_context
def load_classes(ctx, classes_file):
    """Load custom class mapping from a JSON file."""
    config = ctx.obj['config']

    try:
        llm_client = create_llm_client(config)
        bbox_generator = BoundingBoxGenerator(config, llm_client)
        bbox_generator.load_class_mapping(classes_file)

        click.echo(f"Loaded {len(bbox_generator.class_mapping)} classes from {classes_file}")
        for class_name, class_id in sorted(bbox_generator.class_mapping.items(), key=lambda x: x[1]):
            click.echo(f"  {class_id}: {class_name}")

    except Exception as e:
        click.echo(f"Error loading classes: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()
