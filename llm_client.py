"""LLM client interface for different providers."""

import base64
import logging
from abc import ABC, abstractmethod
import httpx

from config import Config

logger = logging.getLogger(__name__)


class LLMClient(ABC):
    """Abstract base class for LLM clients."""

    @abstractmethod
    def analyze_image(self, image_path: str, prompt: str) -> str:
        """Analyze an image and return the response."""
        pass





class OpenRouterClient(LLMClient):
    """OpenRouter client for various models."""

    def __init__(self, config: Config):
        self.config = config
        self.api_key = config.openrouter_api_key
        self.base_url = "https://openrouter.ai/api/v1"
        self.model = config.openrouter_model

    def _encode_image(self, image_path: str) -> str:
        """Encode image to base64."""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def analyze_image(self, image_path: str, prompt: str) -> str:
        """Analyze image using OpenRouter API."""
        try:
            base64_image = self._encode_image(image_path)

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/your-repo",  # Optional
                "X-Title": "LLM Bounding Box Tool"  # Optional
            }

            # Determine the message format based on the model
            if "claude" in self.model.lower():
                # Anthropic format for Claude models
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/jpeg",
                                    "data": base64_image
                                }
                            },
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ]
            else:
                # OpenAI format for GPT models and others
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": self.config.image_quality
                                }
                            }
                        ]
                    }
                ]

            payload = {
                "model": self.model,
                "messages": messages,
                "max_tokens": 1000
            }

            with httpx.Client(timeout=60.0) as client:
                response = client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()

                result = response.json()
                return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"Error analyzing image with OpenRouter: {e}")
            raise


def create_llm_client(config: Config) -> LLMClient:
    """Factory function to create appropriate LLM client."""
    if config.llm_provider == "openrouter":
        return OpenRouterClient(config)
    else:
        raise ValueError(f"Unsupported LLM provider: {config.llm_provider}. Only 'openrouter' is supported.")
