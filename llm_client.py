"""LLM client interface for different providers."""

import base64
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import openai
import anthropic
import google.generativeai as genai
import httpx
from PIL import Image
import io

from config import Config

logger = logging.getLogger(__name__)


class LLMClient(ABC):
    """Abstract base class for LLM clients."""

    @abstractmethod
    def analyze_image(self, image_path: str, prompt: str) -> str:
        """Analyze an image and return the response."""
        pass


class OpenAIClient(LLMClient):
    """OpenAI GPT-4 Vision client."""

    def __init__(self, config: Config):
        self.config = config
        openai.api_key = config.openai_api_key
        self.client = openai.OpenAI(api_key=config.openai_api_key)

    def _encode_image(self, image_path: str) -> str:
        """Encode image to base64."""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def analyze_image(self, image_path: str, prompt: str) -> str:
        """Analyze image using OpenAI GPT-4 Vision."""
        try:
            base64_image = self._encode_image(image_path)

            response = self.client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": self.config.image_quality
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error analyzing image with OpenAI: {e}")
            raise


class AnthropicClient(LLMClient):
    """Anthropic Claude client."""

    def __init__(self, config: Config):
        self.config = config
        self.client = anthropic.Anthropic(api_key=config.anthropic_api_key)

    def _encode_image(self, image_path: str) -> tuple:
        """Encode image to base64 and get media type."""
        with open(image_path, "rb") as image_file:
            image_data = image_file.read()
            base64_image = base64.b64encode(image_data).decode('utf-8')

            # Determine media type
            if image_path.lower().endswith('.png'):
                media_type = "image/png"
            elif image_path.lower().endswith('.jpg') or image_path.lower().endswith('.jpeg'):
                media_type = "image/jpeg"
            else:
                media_type = "image/jpeg"  # Default

            return base64_image, media_type

    def analyze_image(self, image_path: str, prompt: str) -> str:
        """Analyze image using Anthropic Claude."""
        try:
            base64_image, media_type = self._encode_image(image_path)

            response = self.client.messages.create(
                model=self.config.anthropic_model,
                max_tokens=1000,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": media_type,
                                    "data": base64_image
                                }
                            },
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ]
            )

            return response.content[0].text

        except Exception as e:
            logger.error(f"Error analyzing image with Anthropic: {e}")
            raise


class GoogleClient(LLMClient):
    """Google Gemini client."""

    def __init__(self, config: Config):
        self.config = config
        genai.configure(api_key=config.google_api_key)
        self.model = genai.GenerativeModel(config.google_model)

    def analyze_image(self, image_path: str, prompt: str) -> str:
        """Analyze image using Google Gemini."""
        try:
            image = Image.open(image_path)
            response = self.model.generate_content([prompt, image])
            return response.text

        except Exception as e:
            logger.error(f"Error analyzing image with Google: {e}")
            raise


class OpenRouterClient(LLMClient):
    """OpenRouter client for various models."""

    def __init__(self, config: Config):
        self.config = config
        self.api_key = config.openrouter_api_key
        self.base_url = "https://openrouter.ai/api/v1"
        self.model = config.openrouter_model

    def _encode_image(self, image_path: str) -> str:
        """Encode image to base64."""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def analyze_image(self, image_path: str, prompt: str) -> str:
        """Analyze image using OpenRouter API."""
        try:
            base64_image = self._encode_image(image_path)

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/your-repo",  # Optional
                "X-Title": "LLM Bounding Box Tool"  # Optional
            }

            # Determine the message format based on the model
            if "claude" in self.model.lower():
                # Anthropic format for Claude models
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/jpeg",
                                    "data": base64_image
                                }
                            },
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ]
            else:
                # OpenAI format for GPT models and others
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": self.config.image_quality
                                }
                            }
                        ]
                    }
                ]

            payload = {
                "model": self.model,
                "messages": messages,
                "max_tokens": 1000
            }

            with httpx.Client(timeout=60.0) as client:
                response = client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()

                result = response.json()
                return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"Error analyzing image with OpenRouter: {e}")
            raise


def create_llm_client(config: Config) -> LLMClient:
    """Factory function to create appropriate LLM client."""
    if config.llm_provider == "openai":
        return OpenAIClient(config)
    elif config.llm_provider == "anthropic":
        return AnthropicClient(config)
    elif config.llm_provider == "google":
        return GoogleClient(config)
    elif config.llm_provider == "openrouter":
        return OpenRouterClient(config)
    else:
        raise ValueError(f"Unsupported LLM provider: {config.llm_provider}")
