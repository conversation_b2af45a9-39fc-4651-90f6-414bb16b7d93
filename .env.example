# LLM Configuration
LLM_PROVIDER=openrouter
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Model Configuration
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet:beta

# Image Processing
MAX_IMAGE_SIZE=1024
IMAGE_QUALITY=high  # Options: low, high, auto

# Output Configuration
OUTPUT_FORMAT=yolo  # Options: yolo, coco, pascal_voc
CONFIDENCE_THRESHOLD=0.5
OUTPUT_DIR=./output
LABELS_DIR=./labels
VISUALIZATIONS_DIR=./visualizations

# Logging
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR
LOG_FILE=bounding_tool.log
