# LLM Configuration
LLM_PROVIDER=openrouter  # Options: openai, anthropic, google, openrouter
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Model Configuration
OPENAI_MODEL=gpt-4-vision-preview
ANTHROPIC_MODEL=claude-3-sonnet-20240229
GOOGLE_MODEL=gemini-pro-vision
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet:beta

# Image Processing
MAX_IMAGE_SIZE=1024
IMAGE_QUALITY=high  # Options: low, high, auto

# Output Configuration
OUTPUT_FORMAT=yolo  # Options: yolo, coco, pascal_voc
CONFIDENCE_THRESHOLD=0.5
OUTPUT_DIR=./output
LABELS_DIR=./labels
VISUALIZATIONS_DIR=./visualizations

# Logging
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR
LOG_FILE=bounding_tool.log
