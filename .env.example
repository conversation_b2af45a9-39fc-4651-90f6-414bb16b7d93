# LLM Configuration
LLM_PROVIDER=openrouter
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Available Models
MODEL_GEMINI_v2_0_FLASH=google/gemini-2.0-flash-exp:free
MODEL_INTERNVL3_14B=opengvlab/internvl3-14b:free
MODEL_GEMMA_3_27B_IT=google/gemma-3-27b-it:free
MODEL_MISTRALAI_MISTRAL_SMALL_3_1_24B_INSTRUCT=mistralai/mistral-small-3.1-24b-instruct:free
MODEL_QWEN_QWEN2_5_VL_72B_INSTRUCT=qwen/qwen2.5-vl-72b-instruct:free

# Active Model (choose one from above)
OPENROUTER_MODEL=google/gemini-2.0-flash-exp:free

# Image Processing
MAX_IMAGE_SIZE=1024
IMAGE_QUALITY=high  # Options: low, high, auto
STANDARDIZE_IMAGES=true  # Whether to standardize images before processing
STANDARD_WIDTH=1024
STANDARD_HEIGHT=1024
JPEG_QUALITY=85

# Output Configuration
OUTPUT_FORMAT=yolo  # Options: yolo, coco, pascal_voc
CONFIDENCE_THRESHOLD=0.5
OUTPUT_DIR=./output
LABELS_DIR=./labels
VISUALIZATIONS_DIR=./visualizations

# Logging
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR
LOG_FILE=bounding_tool.log
