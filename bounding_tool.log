2025-05-28 19:54:07,295 - __main__ - INFO - <PERSON>M Bounding Box Tool initialized successfully
2025-05-28 20:04:45,944 - __main__ - INFO - LLM Bounding Box Tool initialized successfully
2025-05-28 20:04:54,492 - __main__ - INFO - LLM Bounding Box Tool initialized successfully
2025-05-28 20:09:31,017 - __main__ - INFO - LLM Bounding Box Tool initialized successfully
2025-05-28 20:09:31,063 - image_processor - INFO - Standardized image: C:\bounding\data\milk-packaging-box.jpg -> C:\bounding\data\milk-packaging-box_standardized.jpg (512x512)
2025-05-28 20:09:31,064 - bounding_box_generator - INFO - Generating bounding boxes for: C:\bounding\data\milk-packaging-box_standardized.jpg
2025-05-28 20:09:31,074 - image_processor - INFO - Preprocessed image: C:\bounding\data\milk-packaging-box_standardized.jpg -> C:\bounding\data\milk-packaging-box_standardized.jpg
2025-05-28 20:09:35,340 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 20:09:35,342 - llm_client - ERROR - Error analyzing image with OpenRouter: Client error '429 Too Many Requests' for url 'https://openrouter.ai/api/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429
2025-05-28 20:09:35,342 - __main__ - ERROR - Error processing image: Client error '429 Too Many Requests' for url 'https://openrouter.ai/api/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429
2025-05-28 20:10:31,628 - __main__ - INFO - LLM Bounding Box Tool initialized successfully
2025-05-28 20:10:31,675 - image_processor - INFO - Standardized image: C:\bounding\data\milk-packaging-box.jpg -> C:\bounding\data\milk-packaging-box_standardized.jpg (512x512)
2025-05-28 20:10:31,676 - bounding_box_generator - INFO - Generating bounding boxes for: C:\bounding\data\milk-packaging-box_standardized.jpg
2025-05-28 20:10:31,687 - image_processor - INFO - Preprocessed image: C:\bounding\data\milk-packaging-box_standardized.jpg -> C:\bounding\data\milk-packaging-box_standardized.jpg
2025-05-28 20:10:35,036 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:10:44,077 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:10:44,141 - visualizer - INFO - Saved visualization to: ./visualizations\milk-packaging-box_standardized_visualized.jpg
2025-05-28 20:19:36,349 - __main__ - INFO - LLM Bounding Box Tool initialized successfully
2025-05-28 20:22:22,809 - __main__ - INFO - LLM Bounding Box Tool initialized successfully
2025-05-28 20:22:22,879 - image_processor - INFO - Found 10 valid images in C:\api_recyclable\scripts\dataset_v7\sub_raw
2025-05-28 20:22:22,931 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg (512x512)
2025-05-28 20:22:22,931 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg
2025-05-28 20:22:22,940 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg
2025-05-28 20:22:26,593 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:22:29,203 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:22:29,217 - visualizer - INFO - Saved visualization to: ./visualizations\battery_1_standardized_visualized.jpg
2025-05-28 20:22:29,227 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg (512x512)
2025-05-28 20:22:29,227 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg
2025-05-28 20:22:29,236 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg
2025-05-28 20:22:32,038 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:22:34,486 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:22:34,500 - visualizer - INFO - Saved visualization to: ./visualizations\battery_10_standardized_visualized.jpg
2025-05-28 20:22:34,519 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg (512x512)
2025-05-28 20:22:34,520 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg
2025-05-28 20:22:34,536 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg
2025-05-28 20:22:37,110 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:22:50,804 - bounding_box_generator - INFO - Parsed 4 valid bounding boxes
2025-05-28 20:22:50,810 - visualizer - INFO - Saved visualization to: ./visualizations\battery_2_standardized_visualized.jpg
2025-05-28 20:22:50,818 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg (512x512)
2025-05-28 20:22:50,819 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg
2025-05-28 20:22:50,827 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg
2025-05-28 20:22:54,155 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:22:59,508 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:22:59,515 - visualizer - INFO - Saved visualization to: ./visualizations\battery_3_standardized_visualized.jpg
2025-05-28 20:22:59,524 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg (512x512)
2025-05-28 20:22:59,524 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg
2025-05-28 20:22:59,534 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg
2025-05-28 20:23:02,025 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:23:09,662 - bounding_box_generator - INFO - Parsed 3 valid bounding boxes
2025-05-28 20:23:09,679 - visualizer - INFO - Saved visualization to: ./visualizations\battery_4_standardized_visualized.jpg
2025-05-28 20:23:09,701 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg (512x512)
2025-05-28 20:23:09,701 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg
2025-05-28 20:23:09,723 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg
2025-05-28 20:23:12,486 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:23:14,609 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:23:14,622 - visualizer - INFO - Saved visualization to: ./visualizations\battery_5_standardized_visualized.jpg
2025-05-28 20:23:14,647 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg (512x512)
2025-05-28 20:23:14,650 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg
2025-05-28 20:23:14,663 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg
2025-05-28 20:23:17,425 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:23:19,653 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:23:19,666 - visualizer - INFO - Saved visualization to: ./visualizations\battery_6_standardized_visualized.jpg
2025-05-28 20:23:19,682 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg (512x512)
2025-05-28 20:23:19,682 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg
2025-05-28 20:23:19,694 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg
2025-05-28 20:23:22,570 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:23:28,542 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:23:28,556 - visualizer - INFO - Saved visualization to: ./visualizations\battery_7_standardized_visualized.jpg
2025-05-28 20:23:28,581 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg (512x512)
2025-05-28 20:23:28,582 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg
2025-05-28 20:23:28,602 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg
2025-05-28 20:23:31,143 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:23:36,618 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:23:36,627 - visualizer - INFO - Saved visualization to: ./visualizations\battery_8_standardized_visualized.jpg
2025-05-28 20:23:36,638 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg (512x512)
2025-05-28 20:23:36,638 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg
2025-05-28 20:23:36,647 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg
2025-05-28 20:23:39,310 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:23:44,420 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:23:44,429 - visualizer - INFO - Saved visualization to: ./visualizations\battery_9_standardized_visualized.jpg
2025-05-28 20:23:44,433 - yolo_formatter - INFO - Saved class mapping to: ./labels\classes.json and ./labels\classes.names
2025-05-28 20:25:43,204 - __main__ - INFO - LLM Bounding Box Tool initialized successfully
2025-05-28 20:25:43,219 - image_processor - INFO - Found 20 valid images in C:\api_recyclable\scripts\dataset_v7\sub_raw
2025-05-28 20:25:43,244 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg (1024x1024)
2025-05-28 20:25:43,244 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg
2025-05-28 20:25:43,255 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg
2025-05-28 20:25:46,688 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:25:49,513 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:25:49,544 - visualizer - INFO - Saved visualization to: ./visualizations\battery_1_standardized_visualized.jpg
2025-05-28 20:25:49,582 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg (1024x1024)
2025-05-28 20:25:49,583 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg
2025-05-28 20:25:49,594 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg
2025-05-28 20:25:52,882 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:25:55,507 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:25:55,535 - visualizer - INFO - Saved visualization to: ./visualizations\battery_10_standardized_visualized.jpg
2025-05-28 20:25:55,559 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized_standardized.jpg (1024x1024)
2025-05-28 20:25:55,560 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized_standardized.jpg
2025-05-28 20:25:55,574 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_10_standardized_standardized.jpg
2025-05-28 20:25:58,828 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:26:01,584 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:26:01,617 - visualizer - INFO - Saved visualization to: ./visualizations\battery_10_standardized_standardized_visualized.jpg
2025-05-28 20:26:01,640 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized_standardized.jpg (1024x1024)
2025-05-28 20:26:01,641 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized_standardized.jpg
2025-05-28 20:26:01,653 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_1_standardized_standardized.jpg
2025-05-28 20:26:04,744 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:26:07,502 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:26:07,531 - visualizer - INFO - Saved visualization to: ./visualizations\battery_1_standardized_standardized_visualized.jpg
2025-05-28 20:26:07,576 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg (1024x1024)
2025-05-28 20:26:07,577 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg
2025-05-28 20:26:07,589 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg
2025-05-28 20:26:10,352 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:26:16,818 - bounding_box_generator - INFO - Parsed 3 valid bounding boxes
2025-05-28 20:26:16,834 - visualizer - INFO - Saved visualization to: ./visualizations\battery_2_standardized_visualized.jpg
2025-05-28 20:26:16,846 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized_standardized.jpg (1024x1024)
2025-05-28 20:26:16,846 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized_standardized.jpg
2025-05-28 20:26:16,859 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_2_standardized_standardized.jpg
2025-05-28 20:26:19,590 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:26:26,576 - bounding_box_generator - INFO - Parsed 3 valid bounding boxes
2025-05-28 20:26:26,610 - visualizer - INFO - Saved visualization to: ./visualizations\battery_2_standardized_standardized_visualized.jpg
2025-05-28 20:26:26,647 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg (1024x1024)
2025-05-28 20:26:26,647 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg
2025-05-28 20:26:26,670 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg
2025-05-28 20:26:29,910 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:26:34,347 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:26:34,370 - visualizer - INFO - Saved visualization to: ./visualizations\battery_3_standardized_visualized.jpg
2025-05-28 20:26:34,396 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized_standardized.jpg (1024x1024)
2025-05-28 20:26:34,397 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized_standardized.jpg
2025-05-28 20:26:34,410 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_3_standardized_standardized.jpg
2025-05-28 20:26:37,951 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:26:43,389 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:26:43,424 - visualizer - INFO - Saved visualization to: ./visualizations\battery_3_standardized_standardized_visualized.jpg
2025-05-28 20:26:43,460 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg (1024x1024)
2025-05-28 20:26:43,461 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg
2025-05-28 20:26:43,474 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg
2025-05-28 20:26:46,580 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:26:52,772 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:26:52,801 - visualizer - INFO - Saved visualization to: ./visualizations\battery_4_standardized_visualized.jpg
2025-05-28 20:26:52,828 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized_standardized.jpg (1024x1024)
2025-05-28 20:26:52,829 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized_standardized.jpg
2025-05-28 20:26:52,843 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_4_standardized_standardized.jpg
2025-05-28 20:26:55,737 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:00,068 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:27:00,087 - visualizer - INFO - Saved visualization to: ./visualizations\battery_4_standardized_standardized_visualized.jpg
2025-05-28 20:27:00,111 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg (1024x1024)
2025-05-28 20:27:00,112 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg
2025-05-28 20:27:00,125 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg
2025-05-28 20:27:03,106 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:05,682 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:27:05,712 - visualizer - INFO - Saved visualization to: ./visualizations\battery_5_standardized_visualized.jpg
2025-05-28 20:27:05,742 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized_standardized.jpg (1024x1024)
2025-05-28 20:27:05,745 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized_standardized.jpg
2025-05-28 20:27:05,759 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_5_standardized_standardized.jpg
2025-05-28 20:27:09,723 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:11,912 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:27:11,943 - visualizer - INFO - Saved visualization to: ./visualizations\battery_5_standardized_standardized_visualized.jpg
2025-05-28 20:27:11,980 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg (1024x1024)
2025-05-28 20:27:11,981 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg
2025-05-28 20:27:11,993 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg
2025-05-28 20:27:15,388 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:17,792 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:27:17,823 - visualizer - INFO - Saved visualization to: ./visualizations\battery_6_standardized_visualized.jpg
2025-05-28 20:27:17,843 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized_standardized.jpg (1024x1024)
2025-05-28 20:27:17,843 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized_standardized.jpg
2025-05-28 20:27:17,857 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_6_standardized_standardized.jpg
2025-05-28 20:27:20,765 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:23,364 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:27:23,390 - visualizer - INFO - Saved visualization to: ./visualizations\battery_6_standardized_standardized_visualized.jpg
2025-05-28 20:27:23,441 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg (1024x1024)
2025-05-28 20:27:23,442 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg
2025-05-28 20:27:23,453 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg
2025-05-28 20:27:26,774 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:36,174 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:27:36,207 - visualizer - INFO - Saved visualization to: ./visualizations\battery_7_standardized_visualized.jpg
2025-05-28 20:27:36,232 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized_standardized.jpg (1024x1024)
2025-05-28 20:27:36,234 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized_standardized.jpg
2025-05-28 20:27:36,256 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_7_standardized_standardized.jpg
2025-05-28 20:27:39,496 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:44,802 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:27:44,835 - visualizer - INFO - Saved visualization to: ./visualizations\battery_7_standardized_standardized_visualized.jpg
2025-05-28 20:27:44,869 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg (1024x1024)
2025-05-28 20:27:44,870 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg
2025-05-28 20:27:44,881 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg
2025-05-28 20:27:47,678 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:50,695 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:27:50,717 - visualizer - INFO - Saved visualization to: ./visualizations\battery_8_standardized_visualized.jpg
2025-05-28 20:27:50,734 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized_standardized.jpg (1024x1024)
2025-05-28 20:27:50,736 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized_standardized.jpg
2025-05-28 20:27:50,750 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_8_standardized_standardized.jpg
2025-05-28 20:27:53,701 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:27:56,403 - bounding_box_generator - INFO - Parsed 1 valid bounding boxes
2025-05-28 20:27:56,431 - visualizer - INFO - Saved visualization to: ./visualizations\battery_8_standardized_standardized_visualized.jpg
2025-05-28 20:27:56,465 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg (1024x1024)
2025-05-28 20:27:56,467 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg
2025-05-28 20:27:56,484 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg
2025-05-28 20:27:59,561 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:28:04,411 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:28:04,439 - visualizer - INFO - Saved visualization to: ./visualizations\battery_9_standardized_visualized.jpg
2025-05-28 20:28:04,472 - image_processor - INFO - Standardized image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized_standardized.jpg (1024x1024)
2025-05-28 20:28:04,473 - bounding_box_generator - INFO - Generating bounding boxes for: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized_standardized.jpg
2025-05-28 20:28:04,494 - image_processor - INFO - Preprocessed image: C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized_standardized.jpg -> C:\api_recyclable\scripts\dataset_v7\sub_raw\battery_9_standardized_standardized.jpg
2025-05-28 20:28:07,420 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 20:28:11,662 - bounding_box_generator - INFO - Parsed 2 valid bounding boxes
2025-05-28 20:28:11,692 - visualizer - INFO - Saved visualization to: ./visualizations\battery_9_standardized_standardized_visualized.jpg
2025-05-28 20:28:11,700 - yolo_formatter - INFO - Saved class mapping to: ./labels\classes.json and ./labels\classes.names
