# Usage Examples

## Quick Start

1. **Setup Environment**
```bash
# Copy example configuration
cp .env.example .env

# Edit .env with your OpenRouter API key
# OPENROUTER_API_KEY=your_key_here
```

2. **Test Installation**
```bash
python test_installation.py
```

3. **List Available Models**
```bash
python main.py list-models
```

## Processing Examples

### Process a Single Image
```bash
# Basic processing
python main.py process-image photo.jpg

# With custom classes
python main.py process-image photo.jpg -c person -c car -c dog

# Specify output directories
python main.py process-image photo.jpg -l ./labels -v ./visualizations
```

### Process a Folder of Images
```bash
# Process all images in a folder
python main.py process-folder ./images/

# With custom classes and summary
python main.py process-folder ./images/ -c person -c vehicle --create-summary

# Limit number of images
python main.py process-folder ./images/ --max-images 50
```

## Image Standardization

### Manual Standardization
```bash
# Standardize a single image
python main.py standardize photo.jpg

# Standardize all images in a folder
python main.py standardize ./images/ -o ./standardized/
```

### Automatic Standardization
Set in `.env` file:
```env
STANDARDIZE_IMAGES=true
STANDARD_WIDTH=1024
STANDARD_HEIGHT=1024
JPEG_QUALITY=85
```

Then processing commands will automatically standardize images.

## Model Configuration

### Using Specific Models
Edit `.env` file:
```env
# Set available models
MODEL_GEMINI_v2_0_FLASH=google/gemini-2.0-flash-exp:free
MODEL_QWEN_QWEN2_5_VL_72B_INSTRUCT=qwen/qwen2.5-vl-72b-instruct:free

# Set active model
OPENROUTER_MODEL=google/gemini-2.0-flash-exp:free
```

### Model Performance Tips
- **Gemini 2.0 Flash**: Fast, good for batch processing
- **Qwen 2.5 VL 72B**: High accuracy, slower processing
- **InternVL3 14B**: Balanced speed and accuracy
- **Mistral Small**: Good for simple object detection

## Output Structure

After processing, you'll have:
```
your_project/
├── images/                 # Your input images
├── labels/                 # YOLO format labels
│   ├── photo1.txt         # 0 0.5 0.5 0.3 0.4
│   ├── photo2.txt         # 1 0.2 0.3 0.1 0.2
│   ├── classes.json       # {"person": 0, "car": 1}
│   └── classes.names      # person\ncar\n
└── visualizations/        # Human-friendly images
    ├── photo1_visualized.jpg
    ├── photo2_visualized.jpg
    ├── summary_visualization.jpg
    └── class_statistics.jpg
```

## Advanced Configuration

### Custom Classes
```bash
# Detect only specific objects
python main.py process-folder ./images/ -c person -c bicycle -c car
```

### Quality Settings
```env
# High quality for better accuracy
CONFIDENCE_THRESHOLD=0.7
JPEG_QUALITY=95
STANDARD_WIDTH=1536
STANDARD_HEIGHT=1536

# Fast processing for large datasets
CONFIDENCE_THRESHOLD=0.5
JPEG_QUALITY=75
STANDARD_WIDTH=768
STANDARD_HEIGHT=768
```

### Batch Processing Tips
```bash
# Process large datasets efficiently
python main.py process-folder ./dataset/ --max-images 1000 --create-summary

# Monitor progress
tail -f bounding_tool.log
```

## Troubleshooting

### Common Issues
1. **API Key Error**: Make sure `OPENROUTER_API_KEY` is set in `.env`
2. **No Objects Detected**: Try lowering `CONFIDENCE_THRESHOLD`
3. **Poor Quality**: Enable `STANDARDIZE_IMAGES=true`
4. **Slow Processing**: Use faster models like Gemini Flash

### Debug Mode
```env
LOG_LEVEL=DEBUG
```

### Check Configuration
```bash
python main.py list-models
```

## Integration with YOLOv8

### Training Setup
```python
# dataset.yaml (auto-generated)
train: ./train/images
val: ./val/images
nc: 3
names:
  0: person
  1: car
  2: bicycle
```

### Train YOLOv8 Model
```bash
# Install ultralytics
pip install ultralytics

# Train model
yolo train data=dataset.yaml model=yolov8n.pt epochs=100
```

### Validate Results
Always check the visualization images to verify bounding box accuracy before training!
