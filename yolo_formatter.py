"""YOLO format utilities for bounding box output."""

import os
import json
import logging
from typing import List, Dict, Any
import xml.etree.ElementTree as ET

from bounding_box_generator import BoundingBox
from image_processor import ImageProcessor
from config import Config

logger = logging.getLogger(__name__)


class YOLOFormatter:
    """Handles formatting and saving bounding boxes in various formats."""
    
    def __init__(self, config: Config):
        self.config = config
        self.image_processor = ImageProcessor(config)
    
    def save_yolo_format(self, image_path: str, bounding_boxes: List[BoundingBox], 
                        class_mapping: Dict[str, int], output_dir: str = None) -> str:
        """Save bounding boxes in YOLO format."""
        if output_dir is None:
            output_dir = self.config.output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Get image dimensions
        width, height = self.image_processor.get_image_dimensions(image_path)
        
        # Create output filename
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}.txt")
        
        # Write YOLO format annotations
        with open(output_file, 'w') as f:
            for bbox in bounding_boxes:
                class_id = class_mapping.get(bbox.class_name, len(class_mapping))
                class_name, x_center, y_center, bbox_width, bbox_height = bbox.to_yolo_format(width, height)
                
                # YOLO format: class_id x_center y_center width height
                f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {bbox_width:.6f} {bbox_height:.6f}\n")
        
        logger.info(f"Saved YOLO annotations to: {output_file}")
        return output_file
    
    def save_coco_format(self, image_path: str, bounding_boxes: List[BoundingBox], 
                        class_mapping: Dict[str, int], output_dir: str = None) -> str:
        """Save bounding boxes in COCO format."""
        if output_dir is None:
            output_dir = self.config.output_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Get image info
        image_info = self.image_processor.get_image_info(image_path)
        
        # Create COCO format structure
        coco_data = {
            "images": [
                {
                    "id": 1,
                    "file_name": image_info['filename'],
                    "width": image_info['width'],
                    "height": image_info['height']
                }
            ],
            "annotations": [],
            "categories": []
        }
        
        # Add categories
        for class_name, class_id in class_mapping.items():
            coco_data["categories"].append({
                "id": class_id,
                "name": class_name,
                "supercategory": "object"
            })
        
        # Add annotations
        for i, bbox in enumerate(bounding_boxes):
            class_id = class_mapping.get(bbox.class_name, len(class_mapping))
            
            # COCO format uses x, y, width, height (top-left corner)
            width = bbox.x_max - bbox.x_min
            height = bbox.y_max - bbox.y_min
            area = width * height
            
            annotation = {
                "id": i + 1,
                "image_id": 1,
                "category_id": class_id,
                "bbox": [bbox.x_min, bbox.y_min, width, height],
                "area": area,
                "iscrowd": 0,
                "segmentation": []
            }
            coco_data["annotations"].append(annotation)
        
        # Save to file
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}_coco.json")
        
        with open(output_file, 'w') as f:
            json.dump(coco_data, f, indent=2)
        
        logger.info(f"Saved COCO annotations to: {output_file}")
        return output_file
    
    def save_pascal_voc_format(self, image_path: str, bounding_boxes: List[BoundingBox], 
                              output_dir: str = None) -> str:
        """Save bounding boxes in Pascal VOC format."""
        if output_dir is None:
            output_dir = self.config.output_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Get image info
        image_info = self.image_processor.get_image_info(image_path)
        
        # Create XML structure
        annotation = ET.Element("annotation")
        
        # Add folder and filename
        ET.SubElement(annotation, "folder").text = os.path.dirname(image_path)
        ET.SubElement(annotation, "filename").text = image_info['filename']
        ET.SubElement(annotation, "path").text = image_path
        
        # Add source
        source = ET.SubElement(annotation, "source")
        ET.SubElement(source, "database").text = "Unknown"
        
        # Add size
        size = ET.SubElement(annotation, "size")
        ET.SubElement(size, "width").text = str(image_info['width'])
        ET.SubElement(size, "height").text = str(image_info['height'])
        ET.SubElement(size, "depth").text = "3"
        
        # Add segmented
        ET.SubElement(annotation, "segmented").text = "0"
        
        # Add objects
        for bbox in bounding_boxes:
            obj = ET.SubElement(annotation, "object")
            ET.SubElement(obj, "name").text = bbox.class_name
            ET.SubElement(obj, "pose").text = "Unspecified"
            ET.SubElement(obj, "truncated").text = "0"
            ET.SubElement(obj, "difficult").text = "0"
            
            bndbox = ET.SubElement(obj, "bndbox")
            ET.SubElement(bndbox, "xmin").text = str(int(bbox.x_min))
            ET.SubElement(bndbox, "ymin").text = str(int(bbox.y_min))
            ET.SubElement(bndbox, "xmax").text = str(int(bbox.x_max))
            ET.SubElement(bndbox, "ymax").text = str(int(bbox.y_max))
        
        # Save to file
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}.xml")
        
        tree = ET.ElementTree(annotation)
        tree.write(output_file, encoding='utf-8', xml_declaration=True)
        
        logger.info(f"Saved Pascal VOC annotations to: {output_file}")
        return output_file
    
    def save_class_mapping(self, class_mapping: Dict[str, int], output_dir: str = None) -> str:
        """Save class mapping to a file."""
        if output_dir is None:
            output_dir = self.config.output_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Save as JSON
        mapping_file = os.path.join(output_dir, "classes.json")
        with open(mapping_file, 'w') as f:
            json.dump(class_mapping, f, indent=2)
        
        # Also save as names file (YOLO format)
        names_file = os.path.join(output_dir, "classes.names")
        with open(names_file, 'w') as f:
            # Sort by class ID
            sorted_classes = sorted(class_mapping.items(), key=lambda x: x[1])
            for class_name, _ in sorted_classes:
                f.write(f"{class_name}\n")
        
        logger.info(f"Saved class mapping to: {mapping_file} and {names_file}")
        return mapping_file
    
    def create_dataset_yaml(self, class_mapping: Dict[str, int], 
                           train_path: str = None, val_path: str = None, 
                           output_dir: str = None) -> str:
        """Create a dataset.yaml file for YOLO training."""
        if output_dir is None:
            output_dir = self.config.output_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Create YAML content
        yaml_content = f"""# Dataset configuration for YOLO training
# Generated by LLM Bounding Box Tool

# Dataset paths
train: {train_path or 'path/to/train/images'}
val: {val_path or 'path/to/val/images'}

# Number of classes
nc: {len(class_mapping)}

# Class names
names:
"""
        
        # Add class names sorted by ID
        sorted_classes = sorted(class_mapping.items(), key=lambda x: x[1])
        for i, (class_name, class_id) in enumerate(sorted_classes):
            yaml_content += f"  {class_id}: {class_name}\n"
        
        # Save to file
        yaml_file = os.path.join(output_dir, "dataset.yaml")
        with open(yaml_file, 'w') as f:
            f.write(yaml_content)
        
        logger.info(f"Saved dataset configuration to: {yaml_file}")
        return yaml_file
