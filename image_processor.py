"""Image processing utilities for the bounding box generation tool."""

import os
import logging
from typing import <PERSON>ple, List
import cv2
import numpy as np
from PIL import Image

from config import Config

logger = logging.getLogger(__name__)


class ImageProcessor:
    """Handles image loading, preprocessing, and validation."""
    
    def __init__(self, config: Config):
        self.config = config
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    
    def is_valid_image(self, image_path: str) -> bool:
        """Check if the file is a valid image."""
        if not os.path.exists(image_path):
            return False
        
        file_ext = os.path.splitext(image_path)[1].lower()
        if file_ext not in self.supported_formats:
            return False
        
        try:
            with Image.open(image_path) as img:
                img.verify()
            return True
        except Exception as e:
            logger.warning(f"Invalid image {image_path}: {e}")
            return False
    
    def get_image_dimensions(self, image_path: str) -> Tuple[int, int]:
        """Get image width and height."""
        try:
            with Image.open(image_path) as img:
                return img.size  # (width, height)
        except Exception as e:
            logger.error(f"Error getting image dimensions for {image_path}: {e}")
            raise
    
    def resize_image_if_needed(self, image_path: str, output_path: str = None) -> str:
        """Resize image if it exceeds max_image_size while maintaining aspect ratio."""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                max_size = self.config.max_image_size
                
                # Check if resizing is needed
                if width <= max_size and height <= max_size:
                    return image_path
                
                # Calculate new dimensions maintaining aspect ratio
                if width > height:
                    new_width = max_size
                    new_height = int((height * max_size) / width)
                else:
                    new_height = max_size
                    new_width = int((width * max_size) / height)
                
                # Resize image
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Save resized image
                if output_path is None:
                    base_name = os.path.splitext(image_path)[0]
                    ext = os.path.splitext(image_path)[1]
                    output_path = f"{base_name}_resized{ext}"
                
                resized_img.save(output_path, quality=95)
                logger.info(f"Resized image from {width}x{height} to {new_width}x{new_height}")
                
                return output_path
                
        except Exception as e:
            logger.error(f"Error resizing image {image_path}: {e}")
            raise
    
    def preprocess_image(self, image_path: str) -> str:
        """Preprocess image for LLM analysis."""
        if not self.is_valid_image(image_path):
            raise ValueError(f"Invalid image file: {image_path}")
        
        # Resize if needed
        processed_path = self.resize_image_if_needed(image_path)
        
        logger.info(f"Preprocessed image: {image_path} -> {processed_path}")
        return processed_path
    
    def find_images_in_directory(self, directory: str) -> List[str]:
        """Find all valid images in a directory."""
        image_files = []
        
        if not os.path.exists(directory):
            logger.error(f"Directory does not exist: {directory}")
            return image_files
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                if self.is_valid_image(file_path):
                    image_files.append(file_path)
        
        logger.info(f"Found {len(image_files)} valid images in {directory}")
        return sorted(image_files)
    
    def create_thumbnail(self, image_path: str, thumbnail_size: Tuple[int, int] = (256, 256)) -> str:
        """Create a thumbnail of the image for quick preview."""
        try:
            with Image.open(image_path) as img:
                img.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
                
                base_name = os.path.splitext(image_path)[0]
                ext = os.path.splitext(image_path)[1]
                thumbnail_path = f"{base_name}_thumb{ext}"
                
                img.save(thumbnail_path, quality=85)
                return thumbnail_path
                
        except Exception as e:
            logger.error(f"Error creating thumbnail for {image_path}: {e}")
            raise
    
    def get_image_info(self, image_path: str) -> dict:
        """Get comprehensive information about an image."""
        try:
            with Image.open(image_path) as img:
                info = {
                    'path': image_path,
                    'filename': os.path.basename(image_path),
                    'size': img.size,
                    'width': img.size[0],
                    'height': img.size[1],
                    'mode': img.mode,
                    'format': img.format,
                    'file_size': os.path.getsize(image_path)
                }
                return info
                
        except Exception as e:
            logger.error(f"Error getting image info for {image_path}: {e}")
            raise
