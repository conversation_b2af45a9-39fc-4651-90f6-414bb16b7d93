# LLM Bounding Box Generator for YOLOv8

A powerful tool that utilizes Large Language Models via OpenRouter.ai to automatically generate bounding boxes for images, specifically designed for YOLOv8 training data preparation.

## Features

- **OpenRouter.ai Integration**: Access to 100+ vision models including Claude 3.5 Sonnet, GPT-4 Vision, Llama Vision, etc.
- **Folder Processing**: Process entire folders of images with automatic label generation
- **Visual Verification**: Generate human-friendly images with bounding boxes for verification
- **YOLO Format Output**: Direct output in YOLO training format with normalized coordinates
- **Single Image Processing**: Process individual images with detailed output
- **Custom Classes**: Support for custom object classes
- **Image Preprocessing**: Automatic resizing and optimization
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

## Installation

1. Clone or download this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Copy the example environment file and configure it:
```bash
cp .env.example .env
```

4. Edit `.env` file with your OpenRouter API key and model preferences:
```env
LLM_PROVIDER=openrouter
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Available Models
MODEL_GEMINI_v2_0_FLASH=google/gemini-2.0-flash-exp:free
MODEL_INTERNVL3_14B=opengvlab/internvl3-14b:free
MODEL_GEMMA_3_27B_IT=google/gemma-3-27b-it:free
MODEL_MISTRALAI_MISTRAL_SMALL_3_1_24B_INSTRUCT=mistralai/mistral-small-3.1-24b-instruct:free
MODEL_QWEN_QWEN2_5_VL_72B_INSTRUCT=qwen/qwen2.5-vl-72b-instruct:free

# Active Model
OPENROUTER_MODEL=google/gemini-2.0-flash-exp:free

# Image Standardization
STANDARDIZE_IMAGES=true
STANDARD_WIDTH=1024
STANDARD_HEIGHT=1024
JPEG_QUALITY=85
GENERATE_VISUALIZATIONS=true

# Output Directories (optional - defaults to same level as input)
LABELS_DIR=./labels
VISUALIZATIONS_DIR=./visualizations
```

## Configuration

### Environment Variables

| Variable | Description | Default | Options |
|----------|-------------|---------|---------|
| `LLM_PROVIDER` | LLM service to use | `openrouter` | `openrouter` |
| `OPENROUTER_API_KEY` | OpenRouter API key | - | Your API key |
| `OPENROUTER_MODEL` | Active OpenRouter model | `google/gemini-2.0-flash-exp:free` | Any OpenRouter vision model |
| `MODEL_*` | Available model definitions | - | Model IDs from your environment |
| `STANDARDIZE_IMAGES` | Enable image standardization | `true` | `true`, `false` |
| `STANDARD_WIDTH` | Target image width | `1024` | Any integer |
| `STANDARD_HEIGHT` | Target image height | `1024` | Any integer |
| `JPEG_QUALITY` | JPEG compression quality | `85` | 1-100 |
| `GENERATE_VISUALIZATIONS` | Enable visualization images | `true` | `true`, `false` |
| `MAX_IMAGE_SIZE` | Max image dimension | `1024` | Any integer |
| `CONFIDENCE_THRESHOLD` | Min confidence | `0.5` | 0.0 - 1.0 |
| `LABELS_DIR` | Labels output directory | Auto-generated | Any path |
| `VISUALIZATIONS_DIR` | Visualizations directory | Auto-generated | Any path |

## Usage

### Command Line Interface

The tool provides a CLI with several commands:

#### Process Entire Folder (Recommended)
```bash
python main.py process-folder path/to/images/
```

#### Process a Single Image
```bash
python main.py process-image path/to/image.jpg
```

#### Process with Custom Classes
```bash
python main.py process-folder path/to/images/ -c person -c car -c bicycle
```

#### Specify Output Directories
```bash
python main.py process-folder path/to/images/ -l ./labels -v ./visualizations
```

#### Create Summary Visualizations
```bash
python main.py process-folder path/to/images/ --create-summary
```

#### Standardize Images
```bash
# Standardize a single image
python main.py standardize path/to/image.jpg

# Standardize all images in a folder
python main.py standardize path/to/images/ -o ./standardized/
```

#### List Available Models
```bash
python main.py list-models
```

### Python API

You can also use the tool programmatically:

```python
from config import load_config
from llm_client import create_llm_client
from bounding_box_generator import BoundingBoxGenerator
from yolo_formatter import YOLOFormatter

# Load configuration
config = load_config()

# Create LLM client
llm_client = create_llm_client(config)

# Initialize generator
bbox_generator = BoundingBoxGenerator(config, llm_client)

# Generate bounding boxes
bounding_boxes = bbox_generator.generate_bounding_boxes("image.jpg")

# Save in YOLO format
formatter = YOLOFormatter(config)
formatter.save_yolo_format("image.jpg", bounding_boxes, bbox_generator.class_mapping)
```

## Output Formats

### YOLO Format
- Creates `.txt` files with normalized coordinates
- Format: `class_id x_center y_center width height`
- Generates `classes.names` and `dataset.yaml` files

### COCO Format
- Creates JSON files with absolute coordinates
- Includes image metadata and category information
- Compatible with COCO evaluation tools

### Pascal VOC Format
- Creates XML files with absolute coordinates
- Standard format for object detection datasets
- Compatible with many annotation tools

## File Structure

```
bounding/
├── .env                    # Configuration file
├── .env.example           # Example configuration
├── requirements.txt       # Python dependencies
├── main.py               # CLI interface
├── config.py             # Configuration management
├── llm_client.py         # LLM client implementations
├── image_processor.py    # Image processing utilities
├── bounding_box_generator.py  # Core bounding box logic
├── yolo_formatter.py     # Output formatting
├── README.md             # This file
└── output/               # Generated annotations
    ├── image1.txt        # YOLO annotations
    ├── classes.names     # Class names
    ├── classes.json      # Class mapping
    └── dataset.yaml      # YOLO dataset config
```

## Image Standardization

The tool includes automatic image standardization to ensure consistent quality and size for optimal LLM processing:

### Features
- **Consistent Sizing**: Resize all images to standard dimensions (default: 1024x1024)
- **Aspect Ratio Preservation**: Uses smart cropping to maintain image content
- **Quality Optimization**: Converts to JPEG with configurable quality (default: 85%)
- **Format Normalization**: Converts all images to RGB JPEG format
- **Batch Processing**: Standardize entire folders efficiently

### Configuration
```env
STANDARDIZE_IMAGES=true      # Enable/disable standardization
STANDARD_WIDTH=1024          # Target width in pixels
STANDARD_HEIGHT=1024         # Target height in pixels
JPEG_QUALITY=85             # JPEG quality (1-100)
```

### Usage
```bash
# Automatic standardization during processing (if enabled)
python main.py process-folder images/

# Manual standardization
python main.py standardize images/ -o standardized/
```

### Benefits
- **Improved LLM Performance**: Consistent image sizes improve model accuracy
- **Faster Processing**: Optimized file sizes reduce API call times
- **Storage Efficiency**: Compressed images save disk space
- **Quality Control**: Ensures all images meet minimum quality standards

## Output Structure

When processing images, the tool creates output directories at the same level as your input:

```
your_project/
├── images/                    # Your input images
├── images_labels/             # YOLO format labels (auto-generated)
│   ├── image1.txt            # class_id x_center y_center width height
│   ├── image2.txt
│   ├── classes.json          # Class mapping
│   └── classes.names         # Class names for YOLO
├── images_visualizations/     # Human-friendly verification images (if enabled)
│   ├── image1_visualized.jpg # Images with bounding boxes drawn
│   ├── image2_visualized.jpg
│   ├── summary_visualization.jpg  # Grid view of multiple images
│   └── class_statistics.jpg      # Bar chart of detected classes
└── images_standardized/       # Standardized images (if using manual standardization)
    ├── image1.jpg
    └── image2.jpg
```

### Key Features:
- **Smart Directory Naming**: Output directories are named based on input folder
- **Same Level Structure**: Labels and visualizations are created alongside input folder
- **No Overwriting**: Standardized images are saved separately to avoid loops
- **Optional Visualizations**: Can be disabled via `GENERATE_VISUALIZATIONS=false`

## Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff)
- WebP (.webp)

## API Keys Setup

### OpenRouter
1. Visit [OpenRouter](https://openrouter.ai/keys)
2. Create an account and API key
3. Add to `.env`: `OPENROUTER_API_KEY=your_key_here`
4. Choose from 100+ models including Claude, GPT-4, Llama, etc.

## Tips for Best Results

1. **High-Quality Images**: Use clear, well-lit images for better detection
2. **Image Standardization**: Enable standardization for consistent results
3. **Custom Classes**: Specify relevant classes for your use case
4. **Confidence Threshold**: Adjust based on your quality requirements
5. **Model Selection**: Try different models for best results with your specific images
6. **Batch Processing**: Use folder processing for efficient large datasets

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure your API keys are valid and have sufficient credits
2. **Image Format**: Check that images are in supported formats
3. **Memory Issues**: Reduce `MAX_IMAGE_SIZE` for large images
4. **Rate Limits**: Add delays between requests if hitting API limits

### Logging

Check the log file (`bounding_tool.log`) for detailed error information:
```bash
tail -f bounding_tool.log
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- OpenRouter.ai for providing access to multiple LLM providers
- Anthropic for Claude models
- Google for Gemini models
- OpenAI for GPT-4 Vision models
- Ultralytics for YOLOv8
