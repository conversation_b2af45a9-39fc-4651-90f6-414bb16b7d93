# LLM Bounding Box Generator for YOLOv8

A powerful tool that utilizes Large Language Models (LLMs) to automatically generate bounding boxes for images, specifically designed for YOLOv8 training data preparation.

## Features

- **Multiple LLM Support**: OpenAI GPT-4 Vision, Anthropic Claude, Google Gemini
- **Flexible Configuration**: Easy model switching via environment variables
- **Multiple Output Formats**: YOLO, COCO, Pascal VOC
- **Batch Processing**: Process entire directories of images
- **Custom Classes**: Support for custom object classes
- **Image Preprocessing**: Automatic resizing and optimization
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

## Installation

1. Clone or download this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Copy the example environment file and configure it:
```bash
cp .env.example .env
```

4. Edit `.env` file with your API keys and preferences:
```env
LLM_PROVIDER=openai
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-vision-preview
OUTPUT_FORMAT=yolo
OUTPUT_DIR=./output
```

## Configuration

### Environment Variables

| Variable | Description | Default | Options |
|----------|-------------|---------|---------|
| `LLM_PROVIDER` | LLM service to use | `openai` | `openai`, `anthropic`, `google` |
| `OPENAI_API_KEY` | OpenAI API key | - | Your API key |
| `ANTHROPIC_API_KEY` | Anthropic API key | - | Your API key |
| `GOOGLE_API_KEY` | Google API key | - | Your API key |
| `OPENAI_MODEL` | OpenAI model | `gpt-4-vision-preview` | `gpt-4-vision-preview`, `gpt-4-turbo` |
| `ANTHROPIC_MODEL` | Anthropic model | `claude-3-sonnet-20240229` | `claude-3-sonnet-20240229`, `claude-3-opus-20240229` |
| `GOOGLE_MODEL` | Google model | `gemini-pro-vision` | `gemini-pro-vision`, `gemini-1.5-pro` |
| `MAX_IMAGE_SIZE` | Max image dimension | `1024` | Any integer |
| `OUTPUT_FORMAT` | Output format | `yolo` | `yolo`, `coco`, `pascal_voc` |
| `CONFIDENCE_THRESHOLD` | Min confidence | `0.5` | 0.0 - 1.0 |
| `OUTPUT_DIR` | Output directory | `./output` | Any path |

## Usage

### Command Line Interface

The tool provides a CLI with several commands:

#### Process a Single Image
```bash
python main.py process-image path/to/image.jpg
```

#### Process with Custom Classes
```bash
python main.py process-image path/to/image.jpg -c person -c car -c bicycle
```

#### Process Entire Directory
```bash
python main.py process-directory path/to/images/
```

#### Specify Output Format and Directory
```bash
python main.py process-image image.jpg -f coco -o ./annotations/
```

#### List Available Models
```bash
python main.py list-models
```

### Python API

You can also use the tool programmatically:

```python
from config import load_config
from llm_client import create_llm_client
from bounding_box_generator import BoundingBoxGenerator
from yolo_formatter import YOLOFormatter

# Load configuration
config = load_config()

# Create LLM client
llm_client = create_llm_client(config)

# Initialize generator
bbox_generator = BoundingBoxGenerator(config, llm_client)

# Generate bounding boxes
bounding_boxes = bbox_generator.generate_bounding_boxes("image.jpg")

# Save in YOLO format
formatter = YOLOFormatter(config)
formatter.save_yolo_format("image.jpg", bounding_boxes, bbox_generator.class_mapping)
```

## Output Formats

### YOLO Format
- Creates `.txt` files with normalized coordinates
- Format: `class_id x_center y_center width height`
- Generates `classes.names` and `dataset.yaml` files

### COCO Format
- Creates JSON files with absolute coordinates
- Includes image metadata and category information
- Compatible with COCO evaluation tools

### Pascal VOC Format
- Creates XML files with absolute coordinates
- Standard format for object detection datasets
- Compatible with many annotation tools

## File Structure

```
bounding/
├── .env                    # Configuration file
├── .env.example           # Example configuration
├── requirements.txt       # Python dependencies
├── main.py               # CLI interface
├── config.py             # Configuration management
├── llm_client.py         # LLM client implementations
├── image_processor.py    # Image processing utilities
├── bounding_box_generator.py  # Core bounding box logic
├── yolo_formatter.py     # Output formatting
├── README.md             # This file
└── output/               # Generated annotations
    ├── image1.txt        # YOLO annotations
    ├── classes.names     # Class names
    ├── classes.json      # Class mapping
    └── dataset.yaml      # YOLO dataset config
```

## Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff)
- WebP (.webp)

## API Keys Setup

### OpenAI
1. Visit [OpenAI API](https://platform.openai.com/api-keys)
2. Create an API key
3. Add to `.env`: `OPENAI_API_KEY=your_key_here`

### Anthropic
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Create an API key
3. Add to `.env`: `ANTHROPIC_API_KEY=your_key_here`

### Google
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create an API key
3. Add to `.env`: `GOOGLE_API_KEY=your_key_here`

## Tips for Best Results

1. **High-Quality Images**: Use clear, well-lit images for better detection
2. **Appropriate Resolution**: Images around 1024px work well
3. **Custom Classes**: Specify relevant classes for your use case
4. **Confidence Threshold**: Adjust based on your quality requirements
5. **Batch Processing**: Use directory processing for large datasets

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure your API keys are valid and have sufficient credits
2. **Image Format**: Check that images are in supported formats
3. **Memory Issues**: Reduce `MAX_IMAGE_SIZE` for large images
4. **Rate Limits**: Add delays between requests if hitting API limits

### Logging

Check the log file (`bounding_tool.log`) for detailed error information:
```bash
tail -f bounding_tool.log
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- OpenAI for GPT-4 Vision API
- Anthropic for Claude API
- Google for Gemini API
- Ultralytics for YOLOv8
