"""Configuration management for the bounding box generation tool."""

import os
import logging
from typing import Optional
from dotenv import load_dotenv
from pydantic import BaseModel, field_validator


class Config(BaseModel):
    """Configuration class using Pydantic for validation."""

    # LLM Configuration
    llm_provider: str = "openrouter"
    openrouter_api_key: Optional[str] = None

    # Available Models
    model_gemini_v2_0_flash: Optional[str] = None
    model_internvl3_14b: Optional[str] = None
    model_gemma_3_27b_it: Optional[str] = None
    model_mistralai_mistral_small_3_1_24b_instruct: Optional[str] = None
    model_qwen_qwen2_5_vl_72b_instruct: Optional[str] = None

    # Active Model Configuration
    openrouter_model: str = "google/gemini-2.0-flash-exp:free"

    # Image Processing
    max_image_size: int = 1024
    image_quality: str = "high"
    standardize_images: bool = True
    standard_width: int = 1024
    standard_height: int = 1024
    jpeg_quality: int = 85
    generate_visualizations: bool = True

    # Output Configuration
    output_format: str = "yolo"
    confidence_threshold: float = 0.5
    output_dir: str = "./output"
    labels_dir: str = "./labels"
    visualizations_dir: str = "./visualizations"

    # Logging
    log_level: str = "INFO"
    log_file: str = "bounding_tool.log"

    @field_validator('llm_provider')
    @classmethod
    def validate_llm_provider(cls, v):
        valid_providers = ['openrouter']
        if v not in valid_providers:
            raise ValueError(f'LLM provider must be one of {valid_providers}')
        return v

    @field_validator('output_format')
    @classmethod
    def validate_output_format(cls, v):
        valid_formats = ['yolo', 'coco', 'pascal_voc']
        if v not in valid_formats:
            raise ValueError(f'Output format must be one of {valid_formats}')
        return v

    @field_validator('image_quality')
    @classmethod
    def validate_image_quality(cls, v):
        valid_qualities = ['low', 'high', 'auto']
        if v not in valid_qualities:
            raise ValueError(f'Image quality must be one of {valid_qualities}')
        return v


def load_config() -> Config:
    """Load configuration from environment variables and .env file."""
    load_dotenv()

    # Load from environment variables
    config_data = {}
    env_vars = [
        'llm_provider', 'openrouter_api_key', 'openrouter_model',
        'model_gemini_v2_0_flash', 'model_internvl3_14b', 'model_gemma_3_27b_it',
        'model_mistralai_mistral_small_3_1_24b_instruct', 'model_qwen_qwen2_5_vl_72b_instruct',
        'max_image_size', 'image_quality', 'standardize_images', 'standard_width', 'standard_height', 'jpeg_quality',
        'generate_visualizations', 'output_format', 'confidence_threshold', 'output_dir', 'labels_dir', 'visualizations_dir',
        'log_level', 'log_file'
    ]

    for var in env_vars:
        env_value = os.getenv(var.upper())
        if env_value is not None:
            # Convert numeric values
            if var in ['max_image_size', 'standard_width', 'standard_height', 'jpeg_quality']:
                config_data[var] = int(env_value)
            elif var in ['confidence_threshold']:
                config_data[var] = float(env_value)
            elif var in ['standardize_images', 'generate_visualizations']:
                config_data[var] = env_value.lower() in ('true', '1', 'yes', 'on')
            else:
                config_data[var] = env_value

    return Config(**config_data)


def get_available_models(config: Config) -> dict:
    """Get available models from configuration."""
    models = {}
    if config.model_gemini_v2_0_flash:
        models['gemini_v2_0_flash'] = config.model_gemini_v2_0_flash
    if config.model_internvl3_14b:
        models['internvl3_14b'] = config.model_internvl3_14b
    if config.model_gemma_3_27b_it:
        models['gemma_3_27b_it'] = config.model_gemma_3_27b_it
    if config.model_mistralai_mistral_small_3_1_24b_instruct:
        models['mistralai_mistral_small'] = config.model_mistralai_mistral_small_3_1_24b_instruct
    if config.model_qwen_qwen2_5_vl_72b_instruct:
        models['qwen_qwen2_5_vl_72b'] = config.model_qwen_qwen2_5_vl_72b_instruct
    return models


def setup_logging(config: Config) -> None:
    """Setup logging configuration."""
    log_level = getattr(logging, config.log_level.upper())

    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(config.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)

    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.log_file),
            logging.StreamHandler()
        ]
    )


def validate_api_keys(config: Config) -> None:
    """Validate that required API keys are present."""
    if config.llm_provider == "openrouter" and not config.openrouter_api_key:
        raise ValueError("OpenRouter API key is required when using OpenRouter provider")
