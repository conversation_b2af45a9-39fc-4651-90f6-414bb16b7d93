"""Configuration management for the bounding box generation tool."""

import os
import logging
from typing import Optional
from dotenv import load_dotenv
from pydantic import BaseSettings, validator


class Config(BaseSettings):
    """Configuration class using Pydantic for validation."""
    
    # LLM Configuration
    llm_provider: str = "openai"
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    
    # Model Configuration
    openai_model: str = "gpt-4-vision-preview"
    anthropic_model: str = "claude-3-sonnet-20240229"
    google_model: str = "gemini-pro-vision"
    
    # Image Processing
    max_image_size: int = 1024
    image_quality: str = "high"
    
    # Output Configuration
    output_format: str = "yolo"
    confidence_threshold: float = 0.5
    output_dir: str = "./output"
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "bounding_tool.log"
    
    @validator('llm_provider')
    def validate_llm_provider(cls, v):
        valid_providers = ['openai', 'anthropic', 'google']
        if v not in valid_providers:
            raise ValueError(f'LLM provider must be one of {valid_providers}')
        return v
    
    @validator('output_format')
    def validate_output_format(cls, v):
        valid_formats = ['yolo', 'coco', 'pascal_voc']
        if v not in valid_formats:
            raise ValueError(f'Output format must be one of {valid_formats}')
        return v
    
    @validator('image_quality')
    def validate_image_quality(cls, v):
        valid_qualities = ['low', 'high', 'auto']
        if v not in valid_qualities:
            raise ValueError(f'Image quality must be one of {valid_qualities}')
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


def load_config() -> Config:
    """Load configuration from environment variables and .env file."""
    load_dotenv()
    return Config()


def setup_logging(config: Config) -> None:
    """Setup logging configuration."""
    log_level = getattr(logging, config.log_level.upper())
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(config.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.log_file),
            logging.StreamHandler()
        ]
    )


def validate_api_keys(config: Config) -> None:
    """Validate that required API keys are present."""
    if config.llm_provider == "openai" and not config.openai_api_key:
        raise ValueError("OpenAI API key is required when using OpenAI provider")
    elif config.llm_provider == "anthropic" and not config.anthropic_api_key:
        raise ValueError("Anthropic API key is required when using Anthropic provider")
    elif config.llm_provider == "google" and not config.google_api_key:
        raise ValueError("Google API key is required when using Google provider")
