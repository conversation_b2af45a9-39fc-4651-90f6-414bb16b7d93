"""Visualization utilities for bounding boxes."""

import os
import logging
from typing import List, Tuple
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import hsv_to_rgb

from bounding_box_generator import BoundingBox
from config import Config

logger = logging.getLogger(__name__)


class BoundingBoxVisualizer:
    """Creates visualizations of bounding boxes on images."""
    
    def __init__(self, config: Config):
        self.config = config
        self.colors = self._generate_colors(100)  # Generate 100 distinct colors
    
    def _generate_colors(self, num_colors: int) -> List[Tuple[int, int, int]]:
        """Generate distinct colors for different classes."""
        colors = []
        for i in range(num_colors):
            hue = i / num_colors
            saturation = 0.7 + (i % 3) * 0.1  # Vary saturation slightly
            value = 0.8 + (i % 2) * 0.2  # Vary brightness slightly
            rgb = hsv_to_rgb([hue, saturation, value])
            colors.append(tuple(int(c * 255) for c in rgb))
        return colors
    
    def _get_color_for_class(self, class_name: str, class_id: int = None) -> Tuple[int, int, int]:
        """Get a consistent color for a class."""
        if class_id is not None:
            return self.colors[class_id % len(self.colors)]
        else:
            # Use hash of class name for consistent color
            hash_val = hash(class_name) % len(self.colors)
            return self.colors[hash_val]
    
    def visualize_with_opencv(self, image_path: str, bounding_boxes: List[BoundingBox], 
                             class_mapping: dict, output_path: str = None) -> str:
        """Create visualization using OpenCV."""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            # Convert BGR to RGB for consistent colors
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Draw bounding boxes
            for bbox in bounding_boxes:
                class_id = class_mapping.get(bbox.class_name, len(class_mapping))
                color = self._get_color_for_class(bbox.class_name, class_id)
                
                # Convert coordinates to integers
                x1, y1 = int(bbox.x_min), int(bbox.y_min)
                x2, y2 = int(bbox.x_max), int(bbox.y_max)
                
                # Draw rectangle
                cv2.rectangle(image_rgb, (x1, y1), (x2, y2), color, 2)
                
                # Prepare label text
                label = f"{bbox.class_name} ({bbox.confidence:.2f})"
                
                # Calculate text size
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.6
                thickness = 2
                (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
                
                # Draw label background
                cv2.rectangle(image_rgb, 
                            (x1, y1 - text_height - baseline - 5),
                            (x1 + text_width, y1),
                            color, -1)
                
                # Draw label text
                cv2.putText(image_rgb, label, (x1, y1 - baseline - 2),
                          font, font_scale, (255, 255, 255), thickness)
            
            # Convert back to BGR for saving
            image_bgr = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2BGR)
            
            # Generate output path if not provided
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_dir = self.config.visualizations_dir
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, f"{base_name}_visualized.jpg")
            
            # Save image
            cv2.imwrite(output_path, image_bgr)
            logger.info(f"Saved visualization to: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error creating OpenCV visualization: {e}")
            raise
    
    def visualize_with_matplotlib(self, image_path: str, bounding_boxes: List[BoundingBox], 
                                 class_mapping: dict, output_path: str = None) -> str:
        """Create visualization using Matplotlib."""
        try:
            # Load image
            image = Image.open(image_path)
            
            # Create figure and axis
            fig, ax = plt.subplots(1, figsize=(12, 8))
            ax.imshow(image)
            
            # Draw bounding boxes
            for bbox in bounding_boxes:
                class_id = class_mapping.get(bbox.class_name, len(class_mapping))
                color = np.array(self._get_color_for_class(bbox.class_name, class_id)) / 255.0
                
                # Create rectangle patch
                width = bbox.x_max - bbox.x_min
                height = bbox.y_max - bbox.y_min
                rect = patches.Rectangle((bbox.x_min, bbox.y_min), width, height,
                                       linewidth=2, edgecolor=color, facecolor='none')
                ax.add_patch(rect)
                
                # Add label
                label = f"{bbox.class_name} ({bbox.confidence:.2f})"
                ax.text(bbox.x_min, bbox.y_min - 5, label,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
                       fontsize=10, color='white', weight='bold')
            
            # Remove axis
            ax.set_xlim(0, image.width)
            ax.set_ylim(image.height, 0)
            ax.axis('off')
            
            # Set title
            plt.title(f"Detected Objects: {os.path.basename(image_path)}", fontsize=14, weight='bold')
            
            # Generate output path if not provided
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_dir = self.config.visualizations_dir
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, f"{base_name}_matplotlib.jpg")
            
            # Save figure
            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight', pad_inches=0.1)
            plt.close()
            
            logger.info(f"Saved matplotlib visualization to: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error creating matplotlib visualization: {e}")
            raise
    
    def create_summary_visualization(self, image_paths: List[str], all_bounding_boxes: List[List[BoundingBox]], 
                                   class_mapping: dict, output_path: str = None) -> str:
        """Create a summary visualization showing multiple images with their detections."""
        try:
            # Limit to first 9 images for grid layout
            max_images = min(9, len(image_paths))
            
            # Calculate grid dimensions
            cols = 3
            rows = (max_images + cols - 1) // cols
            
            # Create figure
            fig, axes = plt.subplots(rows, cols, figsize=(15, 5 * rows))
            if rows == 1:
                axes = [axes] if cols == 1 else axes
            else:
                axes = axes.flatten()
            
            for i in range(max_images):
                image_path = image_paths[i]
                bounding_boxes = all_bounding_boxes[i]
                ax = axes[i]
                
                # Load and display image
                image = Image.open(image_path)
                ax.imshow(image)
                
                # Draw bounding boxes
                for bbox in bounding_boxes:
                    class_id = class_mapping.get(bbox.class_name, len(class_mapping))
                    color = np.array(self._get_color_for_class(bbox.class_name, class_id)) / 255.0
                    
                    # Create rectangle patch
                    width = bbox.x_max - bbox.x_min
                    height = bbox.y_max - bbox.y_min
                    rect = patches.Rectangle((bbox.x_min, bbox.y_min), width, height,
                                           linewidth=1, edgecolor=color, facecolor='none')
                    ax.add_patch(rect)
                
                # Set title and remove axis
                ax.set_title(f"{os.path.basename(image_path)}\n{len(bounding_boxes)} objects", 
                           fontsize=10, weight='bold')
                ax.axis('off')
            
            # Hide unused subplots
            for i in range(max_images, len(axes)):
                axes[i].axis('off')
            
            # Generate output path if not provided
            if output_path is None:
                output_dir = self.config.visualizations_dir
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, "summary_visualization.jpg")
            
            # Save figure
            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Saved summary visualization to: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error creating summary visualization: {e}")
            raise
    
    def create_class_statistics_plot(self, all_bounding_boxes: List[List[BoundingBox]], 
                                   class_mapping: dict, output_path: str = None) -> str:
        """Create a plot showing class distribution statistics."""
        try:
            # Count detections per class
            class_counts = {}
            for bounding_boxes in all_bounding_boxes:
                for bbox in bounding_boxes:
                    class_counts[bbox.class_name] = class_counts.get(bbox.class_name, 0) + 1
            
            if not class_counts:
                logger.warning("No detections found for statistics plot")
                return None
            
            # Sort by count
            sorted_classes = sorted(class_counts.items(), key=lambda x: x[1], reverse=True)
            classes, counts = zip(*sorted_classes)
            
            # Create bar plot
            fig, ax = plt.subplots(figsize=(12, 6))
            colors = [np.array(self._get_color_for_class(cls, class_mapping.get(cls, 0))) / 255.0 
                     for cls in classes]
            
            bars = ax.bar(classes, counts, color=colors)
            
            # Customize plot
            ax.set_xlabel('Object Classes', fontsize=12, weight='bold')
            ax.set_ylabel('Number of Detections', fontsize=12, weight='bold')
            ax.set_title('Object Detection Statistics', fontsize=14, weight='bold')
            
            # Rotate x-axis labels if needed
            if len(classes) > 10:
                plt.xticks(rotation=45, ha='right')
            
            # Add value labels on bars
            for bar, count in zip(bars, counts):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{count}', ha='center', va='bottom', fontweight='bold')
            
            # Generate output path if not provided
            if output_path is None:
                output_dir = self.config.visualizations_dir
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, "class_statistics.jpg")
            
            # Save figure
            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Saved class statistics plot to: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error creating class statistics plot: {e}")
            raise
